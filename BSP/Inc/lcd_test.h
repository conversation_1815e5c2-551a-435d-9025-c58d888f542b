/* ========================================================================================
 * LCD和触摸测试函数库
 * ========================================================================================
 * @file    lcd_test.h
 * @brief   LCD显示驱动和触摸功能测试头文件
 * <AUTHOR> Project Team
 * @version V2.0
 * @date    2025-01-27
 *
 * @note    功能特性：
 *          - LCD基础功能测试（颜色、性能、多层等）
 *          - 触摸手势测试（单击、滑动等）
 *          - 触摸坐标验证和调试
 *          - 横竖屏切换测试
 *          - 综合功能测试
 * ======================================================================================== */

#ifndef INC_LCD_TEST_H_
#define INC_LCD_TEST_H_

#include "lcd.h"
#include "touch.h"

/* ========================================================================================
 * 测试函数声明
 * ======================================================================================== */

/* ----------------------------------------------------------------------------------------
 * 1. LCD基础功能测试
 * ---------------------------------------------------------------------------------------- */

/**
 * @brief  LCD基础功能测试
 * @param  None
 * @retval None
 */
void LCD_BasicTest(void);

#ifdef LCD_MULTI_LAYER_ENABLE
/**
 * @brief  LCD多层功能测试
 * @param  None
 * @retval None
 */
void LCD_MultiLayerTest(void);
#endif

/**
 * @brief  LCD性能测试
 * @param  None
 * @retval None
 */
void LCD_PerformanceTest(void);

/**
 * @brief  LCD颜色测试
 * @param  None
 * @retval None
 */
void LCD_ColorTest(void);

/**
 * @brief  LCD综合测试
 * @param  None
 * @retval None
 */
void LCD_ComprehensiveTest(void);

/**
 * @brief  LCD_SetColor函数专项测试
 * @param  None
 * @retval None
 */
void LCD_SetColorTest(void);

/* ----------------------------------------------------------------------------------------
 * 2. 触摸功能测试
 * ---------------------------------------------------------------------------------------- */

/**
 * @brief  Touch_WaitForInput 手势测试函数
 * @note   测试Touch_WaitForInput支持的5种手势：单击、上滑、下滑、左滑、右滑
 * @param  None
 * @retval None
 */
void LCD_TouchGestureTest(void);

/**
 * @brief  触摸坐标验证测试函数
 * @note   测试触摸坐标与LCD坐标的匹配性，验证坐标转换是否正确
 * @param  None
 * @retval None
 */
void LCD_TouchCoordinateTest(void);

/* ----------------------------------------------------------------------------------------
 * 3. 触摸调试和高级测试
 * ---------------------------------------------------------------------------------------- */

/**
 * @brief  触摸坐标转换调试测试函数
 * @note   详细显示触摸原始坐标和转换后坐标，用于调试坐标转换逻辑
 * @param  None
 * @retval None
 */
void LCD_TouchTransformDebugTest(void);

/**
 * @brief  横竖屏切换触摸测试函数
 * @note   测试触摸系统在横竖屏切换时的适应性
 * @param  None
 * @retval None
 */
void LCD_TouchOrientationSwitchTest(void);

/**
 * @brief  检查LTDC Alpha值测试函数
 * @note   显示LCD_Init()后的LTDC层Alpha配置值
 * @param  None
 * @retval None
 */
void LCD_CheckAlphaValueTest(void);

/**
 * @brief  LTDC Alpha值动态测试函数
 * @note   动态调整Alpha值并观察效果
 * @param  None
 * @retval None
 */
void LCD_AlphaDynamicTest(void);

#endif /* INC_LCD_TEST_H_ */
