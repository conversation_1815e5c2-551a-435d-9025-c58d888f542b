
#ifndef __W25QXX_H
#define __W25QXX_H

#ifdef __cplusplus
extern "C" {
#endif

#include "main.h"

/* 通信方式选择 - 只能选择一种 */
#define W25QXX_USE_QSPI     1   // QSPI模式
#define W25QXX_USE_SPI1     2   // SPI1模式  
#define W25QXX_USE_SPI2     3   // SPI2模式

/* 选择通信接口 - 只能选择其中一个值 */
#define W25QXX_INTERFACE    W25QXX_USE_QSPI

/* 根据接口选择设置对应的宏 */
#if (W25QXX_INTERFACE == W25QXX_USE_QSPI)
    #define USE_QSPI_MODE   1
    #define USE_SPI_MODE    0
    #define USE_SPI1        0
    #define USE_SPI2        0
#elif (W25QXX_INTERFACE == W25QXX_USE_SPI1)
    #define USE_QSPI_MODE   0
    #define USE_SPI_MODE    1
    #define USE_SPI1        1
    #define USE_SPI2        0
#elif (W25QXX_INTERFACE == W25QXX_USE_SPI2)
    #define USE_QSPI_MODE   0
    #define USE_SPI_MODE    1
    #define USE_SPI1        0
    #define USE_SPI2        1
#else
    #error "请选择正确的通信接口: W25QXX_USE_QSPI, W25QXX_USE_SPI1, 或 W25QXX_USE_SPI2"
#endif

/* 芯片型号选择 - 只能选择一种 */
#define USE_W25Q80          0
#define USE_W25Q16          0
#define USE_W25Q32          0
#define USE_W25Q64          0
#define USE_W25Q128         0
#define USE_W25Q256         1

/* 地址模式和芯片参数配置 */
#if USE_W25Q256
    #define W25QXX_4BYTE_ADDR_MODE    1
    #define W25QXX_ADDR_SIZE          QSPI_ADDRESS_32_BITS
    #define W25QXX_ADDR_BYTES         4
    #define W25QXX_CHIP_SIZE          (32 * 1024 * 1024)  // 32MB
    #define W25QXX_CHIP_ID            0xEF18
    #define W25QXX_PAGE_COUNT         (W25QXX_CHIP_SIZE / W25QXX_PAGE_SIZE)      // 131072页
    #define W25QXX_SECTOR_COUNT       (W25QXX_CHIP_SIZE / W25QXX_SECTOR_SIZE)    // 8192扇区
    #define W25QXX_BLOCK_COUNT        (W25QXX_CHIP_SIZE / W25QXX_BLOCK_SIZE)     // 512块
#elif USE_W25Q128
    #define W25QXX_4BYTE_ADDR_MODE    0
    #define W25QXX_ADDR_SIZE          QSPI_ADDRESS_24_BITS
    #define W25QXX_ADDR_BYTES         3
    #define W25QXX_CHIP_SIZE          (16 * 1024 * 1024)  // 16MB
    #define W25QXX_CHIP_ID            0xEF17
    #define W25QXX_PAGE_COUNT         (W25QXX_CHIP_SIZE / W25QXX_PAGE_SIZE)      // 65536页
    #define W25QXX_SECTOR_COUNT       (W25QXX_CHIP_SIZE / W25QXX_SECTOR_SIZE)    // 4096扇区
    #define W25QXX_BLOCK_COUNT        (W25QXX_CHIP_SIZE / W25QXX_BLOCK_SIZE)     // 256块
#elif USE_W25Q64
    #define W25QXX_4BYTE_ADDR_MODE    0
    #define W25QXX_ADDR_SIZE          QSPI_ADDRESS_24_BITS
    #define W25QXX_ADDR_BYTES         3
    #define W25QXX_CHIP_SIZE          (8 * 1024 * 1024)   // 8MB
    #define W25QXX_CHIP_ID            0xEF16
    #define W25QXX_PAGE_COUNT         (W25QXX_CHIP_SIZE / W25QXX_PAGE_SIZE)      // 32768页
    #define W25QXX_SECTOR_COUNT       (W25QXX_CHIP_SIZE / W25QXX_SECTOR_SIZE)    // 2048扇区
    #define W25QXX_BLOCK_COUNT        (W25QXX_CHIP_SIZE / W25QXX_BLOCK_SIZE)     // 128块
#elif USE_W25Q32
    #define W25QXX_4BYTE_ADDR_MODE    0
    #define W25QXX_ADDR_SIZE          QSPI_ADDRESS_24_BITS
    #define W25QXX_ADDR_BYTES         3
    #define W25QXX_CHIP_SIZE          (4 * 1024 * 1024)   // 4MB
    #define W25QXX_CHIP_ID            0xEF15
    #define W25QXX_PAGE_COUNT         (W25QXX_CHIP_SIZE / W25QXX_PAGE_SIZE)      // 16384页
    #define W25QXX_SECTOR_COUNT       (W25QXX_CHIP_SIZE / W25QXX_SECTOR_SIZE)    // 1024扇区
    #define W25QXX_BLOCK_COUNT        (W25QXX_CHIP_SIZE / W25QXX_BLOCK_SIZE)     // 64块
#elif USE_W25Q16
    #define W25QXX_4BYTE_ADDR_MODE    0
    #define W25QXX_ADDR_SIZE          QSPI_ADDRESS_24_BITS
    #define W25QXX_ADDR_BYTES         3
    #define W25QXX_CHIP_SIZE          (2 * 1024 * 1024)   // 2MB
    #define W25QXX_CHIP_ID            0xEF14
    #define W25QXX_PAGE_COUNT         (W25QXX_CHIP_SIZE / W25QXX_PAGE_SIZE)      // 8192页
    #define W25QXX_SECTOR_COUNT       (W25QXX_CHIP_SIZE / W25QXX_SECTOR_SIZE)    // 512扇区
    #define W25QXX_BLOCK_COUNT        (W25QXX_CHIP_SIZE / W25QXX_BLOCK_SIZE)     // 32块
#elif USE_W25Q80
    #define W25QXX_4BYTE_ADDR_MODE    0
    #define W25QXX_ADDR_SIZE          QSPI_ADDRESS_24_BITS
    #define W25QXX_ADDR_BYTES         3
    #define W25QXX_CHIP_SIZE          (1 * 1024 * 1024)   // 1MB
    #define W25QXX_CHIP_ID            0xEF13
    #define W25QXX_PAGE_COUNT         (W25QXX_CHIP_SIZE / W25QXX_PAGE_SIZE)      // 4096页
    #define W25QXX_SECTOR_COUNT       (W25QXX_CHIP_SIZE / W25QXX_SECTOR_SIZE)    // 256扇区
    #define W25QXX_BLOCK_COUNT        (W25QXX_CHIP_SIZE / W25QXX_BLOCK_SIZE)     // 16块
#endif

/* 通用参数定义 */
#define W25QXX_PAGE_SIZE        256     // 页大小 256字节
#define W25QXX_SECTOR_SIZE      4096    // 扇区大小 4KB
#define W25QXX_BLOCK_SIZE       65536   // 块大小 64KB

/* 超时时间定义 */
#define W25QXX_TIMEOUT          1000        // 通用操作超时时间(ms)
#define W25QXX_SECTOR_ERASE_TIMEOUT  3000   // 扇区擦除超时时间(ms)
#define W25QXX_BLOCK_ERASE_TIMEOUT   10000  // 块擦除超时时间(ms)
#define W25QXX_CHIP_ERASE_TIMEOUT    200000 // 整片擦除超时时间(ms) - 约3.3分钟

/* W25QXX指令集 */
#define W25X_WriteEnable        0x06
#define W25X_WriteDisable       0x04
#define W25X_ReadStatusReg1     0x05
#define W25X_ReadStatusReg2     0x35
#define W25X_WriteStatusReg     0x01
#define W25X_ReadData           0x03
#define W25X_FastReadData       0x0B
#define W25X_FastReadDual       0x3B
#define W25X_PageProgram        0x02
#define W25X_BlockErase         0xD8
#define W25X_SectorErase        0x20
#define W25X_ChipErase          0xC7
#define W25X_PowerDown          0xB9
#define W25X_ReleasePowerDown   0xAB
#define W25X_DeviceID           0xAB
#define W25X_ManufactDeviceID   0x90
#define W25X_JedecDeviceID      0x9F
#define W25X_Enter4ByteMode     0xB7
#define W25X_Exit4ByteMode      0xE9

/* 硬件接口定义 */
#if USE_QSPI_MODE
    extern QSPI_HandleTypeDef hqspi;
    #define W25QXX_QSPI_HANDLE  hqspi
#elif USE_SPI_MODE
    #if USE_SPI1
        extern SPI_HandleTypeDef hspi1;
        #define W25QXX_SPI_HANDLE   hspi1
        #define W25QXX_CS_PIN       GPIO_PIN_4
        #define W25QXX_CS_PORT      GPIOA
    #elif USE_SPI2
        extern SPI_HandleTypeDef hspi2;
        #define W25QXX_SPI_HANDLE   hspi2
        #define W25QXX_CS_PIN       GPIO_PIN_12
        #define W25QXX_CS_PORT      GPIOB
    #endif
    
    #define W25QXX_CS_LOW()     HAL_GPIO_WritePin(W25QXX_CS_PORT, W25QXX_CS_PIN, GPIO_PIN_RESET)
    #define W25QXX_CS_HIGH()    HAL_GPIO_WritePin(W25QXX_CS_PORT, W25QXX_CS_PIN, GPIO_PIN_SET)
#endif

/* 函数声明 */
HAL_StatusTypeDef W25QXX_Init(void);
uint16_t W25QXX_ReadID(void);
uint32_t W25QXX_ReadJedecID(void);
HAL_StatusTypeDef W25QXX_WriteEnable(void);
HAL_StatusTypeDef W25QXX_WriteDisable(void);
uint8_t W25QXX_ReadStatusReg1(void);
HAL_StatusTypeDef W25QXX_WaitForWriteEnd(uint32_t timeout_ms);
HAL_StatusTypeDef W25QXX_PowerDown(void);
HAL_StatusTypeDef W25QXX_WakeUp(void);

HAL_StatusTypeDef W25QXX_ReadData(uint32_t addr, uint8_t* pBuffer, uint32_t numByteToRead);
HAL_StatusTypeDef W25QXX_FastRead(uint32_t addr, uint8_t* pBuffer, uint32_t numByteToRead);
HAL_StatusTypeDef W25QXX_WritePage(uint32_t addr, uint8_t* pBuffer, uint32_t numByteToWrite);
HAL_StatusTypeDef W25QXX_WriteData(uint32_t addr, uint8_t* pBuffer, uint32_t numByteToWrite);

HAL_StatusTypeDef W25QXX_EraseSector(uint32_t sectorAddr);
HAL_StatusTypeDef W25QXX_EraseBlock(uint32_t blockAddr);
HAL_StatusTypeDef W25QXX_EraseChip(void);

/* 文件系统专用函数 */
HAL_StatusTypeDef W25QXX_WriteSectors(uint32_t sector, const uint8_t* buff, uint32_t count);
HAL_StatusTypeDef W25QXX_ReadSectors(uint32_t sector, uint8_t* buff, uint32_t count);

#ifdef __cplusplus
}
#endif

#endif /* __W25QXX_H */
