//
// Created by wang on 2025/7/15.
//

#ifndef MPU6050_MPU6050_H
#define MPU6050_MPU6050_H

#include "i2c.h"
#include <stdint.h>

// MPU6050 I2C地址
#define MPU6050_ADDRESS 0xD0

// 主要寄存器
#define MPU6050_RA_PWR_MGMT_1       0x6B
#define MPU6050_RA_SMPLRT_DIV       0x19
#define MPU6050_RA_CONFIG           0x1A
#define MPU6050_RA_GYRO_CONFIG      0x1B
#define MPU6050_RA_ACCEL_CONFIG     0x1C
#define MPU6050_RA_ACCEL_XOUT_H     0x3B
#define MPU6050_RA_TEMP_OUT_H       0x41
#define MPU6050_RA_GYRO_XOUT_H      0x43
#define MPU6050_RA_WHO_AM_I         0x75

// 灵敏度
#define MPU6050_ACCEL_FS_2G        0x00
#define MPU6050_GYRO_FS_2000       0x18
#define MPU6050_DLPF_BW_42         0x03

// 数据结构
typedef struct {
    float x, y, z;
} MPU6050_Vector3f_t;

typedef struct {
    MPU6050_Vector3f_t accel;
    MPU6050_Vector3f_t gyro;
    float temperature;
} MPU6050_Data_t;

// 只保留以下5个函数声明
void MPU6050_Init(void);
int MPU6050_CalibrateOffsets(void);
int MPU6050_Euler(float *yaw, float *pitch, float *roll);
int MPU6050_ReadRaw(float *ax, float *ay, float *az, float *temperature, float *gx, float *gy, float *gz);
int MPU6050_ReadData(float *ax, float *ay, float *az, float *temperature, float *gx, float *gy, float *gz, uint8_t keep_gravity);

#endif //MPU6050_MPU6050_H
