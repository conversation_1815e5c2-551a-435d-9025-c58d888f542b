/**
 ******************************************************************************
 * @file    file_opera.h
 * @brief   文件操作库头文件 - 提供常用的FatFS文件系统操作函数
 * <AUTHOR> Project Team
 * @version V1.0
 * @date    2025-08-02
 ******************************************************************************
 * @attention
 *
 * 功能特性：
 * - 基于FatFS文件系统的高级文件操作封装
 * - 支持文件和目录的创建、删除、复制、移动等操作
 * - 提供4K对齐优化的读写操作
 * - 支持批量文件操作和磁盘信息查询
 * - 完善的错误处理和状态返回机制
 * - 兼容STM32 HAL库和W25QXX Flash存储
 *
 * 使用示例：
 * @code
 * // 1. 基本文件操作
 * file_result_t result;
 * result = File_Create("test.txt", "Hello World!");
 * result = File_Copy("test.txt", "backup.txt");
 * result = File_Move("backup.txt", "archive/backup.txt");
 *
 * // 2. 目录操作
 * result = Dir_Create("data");
 * file_info_t files[10];
 * uint32_t count;
 * result = Dir_List("data", files, 10, &count);
 *
 * // 3. 4K对齐读写（优化Flash性能）
 * uint8_t data[4096];
 * result = File_Write4K("large_file.bin", data, sizeof(data));
 *
 * // 4. 磁盘信息查询
 * disk_info_t disk;
 * result = Disk_GetInfo(&disk);
 * printf("Disk usage: %d%%\n", disk.usage_percent);
 *
 * // 5. 批量操作
 * result = File_BatchDelete("temp", "*.tmp", NULL);
 * @endcode
 *
 ******************************************************************************
 */

#ifndef __FILE_OPERA_H
#define __FILE_OPERA_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "ff.h"
#include "fatfs.h"
#include <string.h>
#include <stdio.h>
#include <stdlib.h>

/* Exported constants --------------------------------------------------------*/

/** @defgroup FILE_OPERA_Constants 文件操作常量定义
  * @{
  */

/* 路径和文件名长度限制 */
#define FILE_MAX_PATH_LEN           256     ///< 最大路径长度
#define FILE_MAX_NAME_LEN           64      ///< 最大文件名长度
#define FILE_MAX_EXT_LEN            16      ///< 最大扩展名长度

/* 缓冲区大小定义 */
#define FILE_BUFFER_SIZE            4096    ///< 默认文件操作缓冲区大小
#define FILE_4K_ALIGNED_SIZE        4096    ///< 4K对齐缓冲区大小
#define FILE_COPY_BUFFER_SIZE       8192    ///< 文件复制缓冲区大小

/* 批量操作限制 */
#define FILE_MAX_BATCH_COUNT        100     ///< 最大批量操作文件数量
#define FILE_MAX_DIR_DEPTH          8       ///< 最大目录递归深度

/**
  * @}
  */

/* Exported types ------------------------------------------------------------*/

/** @defgroup FILE_OPERA_Types 文件操作类型定义
  * @{
  */

/**
 * @brief 文件操作结果枚举
 */
typedef enum {
    FILE_OK = 0,                    ///< 操作成功
    FILE_ERROR,                     ///< 一般错误
    FILE_ERROR_INVALID_PARAM,       ///< 无效参数
    FILE_ERROR_FILE_NOT_FOUND,      ///< 文件未找到
    FILE_ERROR_DIR_NOT_FOUND,       ///< 目录未找到
    FILE_ERROR_FILE_EXISTS,         ///< 文件已存在
    FILE_ERROR_DIR_EXISTS,          ///< 目录已存在
    FILE_ERROR_NO_SPACE,            ///< 磁盘空间不足
    FILE_ERROR_ACCESS_DENIED,       ///< 访问被拒绝
    FILE_ERROR_BUFFER_TOO_SMALL,    ///< 缓冲区太小
    FILE_ERROR_PATH_TOO_LONG,       ///< 路径太长
    FILE_ERROR_FATFS_ERROR          ///< FatFS底层错误
} file_result_t;

/**
 * @brief 文件操作类型枚举
 */
typedef enum {
    FILE_OP_CREATE = 0,             ///< 创建文件
    FILE_OP_DELETE,                 ///< 删除文件
    FILE_OP_COPY,                   ///< 复制文件
    FILE_OP_MOVE,                   ///< 移动文件
    FILE_OP_RENAME,                 ///< 重命名文件
    FILE_OP_LIST                    ///< 列出文件
} file_operation_t;

/**
 * @brief 文件信息结构体
 */
typedef struct {
    char name[FILE_MAX_NAME_LEN];   ///< 文件名
    uint32_t size;                  ///< 文件大小（字节）
    uint16_t date;                  ///< 修改日期
    uint16_t time;                  ///< 修改时间
    uint8_t attrib;                 ///< 文件属性
    uint8_t is_directory;           ///< 是否为目录（1-目录，0-文件）
} file_info_t;

/**
 * @brief 磁盘信息结构体
 */
typedef struct {
    uint32_t total_size;            ///< 总容量（KB）
    uint32_t free_size;             ///< 可用空间（KB）
    uint32_t used_size;             ///< 已用空间（KB）
    uint8_t usage_percent;          ///< 使用率百分比
    uint8_t fat_type;               ///< FAT类型（12/16/32）
    uint16_t sector_size;           ///< 扇区大小（字节）
    uint32_t cluster_size;          ///< 簇大小（字节）
} disk_info_t;

/**
 * @brief 批量操作回调函数类型
 * @param filename 当前处理的文件名
 * @param index 当前文件索引
 * @param total 总文件数
 * @param result 操作结果
 */
typedef void (*file_batch_callback_t)(const char* filename, uint32_t index, uint32_t total, file_result_t result);

/**
  * @}
  */

/* Exported functions --------------------------------------------------------*/

/** @defgroup FILE_OPERA_Functions 文件操作函数
  * @{
  */

/* 文件基本操作函数 */
file_result_t File_Create(const char* filename, const char* initial_data);
file_result_t File_Delete(const char* filename);
file_result_t File_Rename(const char* old_name, const char* new_name);
file_result_t File_Copy(const char* src_file, const char* dst_file);
file_result_t File_Move(const char* src_file, const char* dst_file);
file_result_t File_Exists(const char* filename, uint8_t* exists);

/* 目录操作函数 */
file_result_t Dir_Create(const char* dirname);
file_result_t Dir_Delete(const char* dirname);
file_result_t Dir_Exists(const char* dirname, uint8_t* exists);
file_result_t Dir_List(const char* dirname, file_info_t* file_list, uint32_t max_count, uint32_t* actual_count);

/* 文件读写操作函数 */
file_result_t File_Write(const char* filename, const void* data, uint32_t size);
file_result_t File_Read(const char* filename, void* buffer, uint32_t size, uint32_t* bytes_read);
file_result_t File_Append(const char* filename, const void* data, uint32_t size);

/* 4K对齐优化读写函数 */
file_result_t File_Write4K(const char* filename, const void* data, uint32_t size);
file_result_t File_Read4K(const char* filename, void* buffer, uint32_t size, uint32_t* bytes_read);

/* 文件信息获取函数 */
file_result_t File_GetInfo(const char* filename, file_info_t* info);
file_result_t File_GetSize(const char* filename, uint32_t* size);

/* 批量操作函数 */
file_result_t File_BatchOperation(const char* pattern, const char* target_dir, 
                                  file_operation_t operation, file_batch_callback_t callback);
file_result_t File_BatchDelete(const char* dirname, const char* pattern, file_batch_callback_t callback);

/* 磁盘信息函数 */
file_result_t Disk_GetInfo(disk_info_t* info);
file_result_t Disk_Format(void);
file_result_t Disk_CleanAll(void);

/* 工具函数 */
const char* File_GetErrorString(file_result_t result);
file_result_t File_ConvertFatFSError(FRESULT fr);

/**
  * @}
  */

#ifdef __cplusplus
}
#endif

#endif /* __FILE_OPERA_H */
