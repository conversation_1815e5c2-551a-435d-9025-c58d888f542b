# STM32 LCD和触摸驱动库

## 项目概述

这是一个完整的STM32 LCD显示和触摸控制系统，包含GT1158/GT9xx触摸芯片驱动和LCD显示驱动，支持横竖屏动态切换、多点触摸、手势识别等功能。

## 项目结构

```
BSP/
├── Inc/
│   ├── lcd.h                    # LCD显示驱动头文件
│   ├── touch.h                  # 触摸芯片驱动头文件
│   └── lcd_test.h               # 测试函数头文件
├── Src/
│   ├── lcd.c                    # LCD显示驱动实现
│   ├── touch.c                  # 触摸芯片驱动实现
│   └── lcd_test.c               # 测试函数实现
├── README.md                    # 项目总览（本文件）
├── README_Touch.md              # 触摸驱动详细文档
└── README_LCD_Test.md           # 测试函数详细文档
```

## 功能特性

### 🖥️ LCD显示功能
- **多种显示模式**：支持横屏和竖屏显示
- **图形绘制**：点、线、矩形、圆形等基本图形
- **文字显示**：多种字体大小和颜色
- **高性能**：优化的绘制算法和DMA支持

### 🎯 触摸控制功能
- **多点触摸**：最多支持5个触摸点
- **手势识别**：单击、长按、上滑、下滑、左滑、右滑
- **横竖屏适配**：自动适配屏幕方向变化
- **防误触**：边缘检测、防抖处理
- **中断驱动**：高效的事件处理机制

### 🔧 测试和调试功能
- **LCD功能测试**：颜色、性能、多层测试
- **触摸功能测试**：手势识别、坐标验证
- **调试工具**：坐标转换调试、横竖屏切换测试
- **综合测试**：完整的系统功能验证

## 硬件要求

### LCD显示屏
- **分辨率**：800x480
- **接口**：RGB/LTDC接口
- **颜色深度**：16位或24位

### 触摸屏
- **芯片型号**：GT1158/GT9xx系列
- **接口**：I2C2
- **连接引脚**：
  - SDA: PB11
  - SCL: PB10
  - RST: PI8
  - INT: PD13

### STM32控制器
- **系列**：STM32F4/F7/H7系列
- **外设要求**：I2C2、LTDC、DMA
- **内存要求**：至少512KB Flash，256KB RAM

## 快速开始

### 1. 基础配置

```c
#include "lcd.h"
#include "touch.h"

int main(void)
{
    // 系统初始化
    HAL_Init();
    SystemClock_Config();
    MX_GPIO_Init();
    MX_I2C2_Init();
    MX_LTDC_Init();
    
    // LCD和触摸初始化
    LCD_Init();
    Touch_Init();
    
    // 主循环
    while (1) {
        Touch_ProcessEvents();
        HAL_Delay(10);
    }
}
```

### 2. 基础显示

```c
// 清屏
LCD_Clear(LCD_COLOR_WHITE);

// 设置颜色
LCD_SetColor(LCD_COLOR_BLACK, LCD_COLOR_WHITE);

// 显示文字
LCD_ShowStringSimple(10, 10, 16, "Hello World!");

// 绘制图形
LCD_FillRect(50, 50, 150, 100, LCD_COLOR_BLUE);
LCD_DrawCircle(200, 200, 50, LCD_COLOR_RED);
```

### 3. 触摸处理

```c
// 等待触摸手势
touch_gesture_type_t gesture = Touch_WaitForInput(3000);

switch (gesture) {
    case TOUCH_GESTURE_CLICK:
        LCD_ShowStringSimple(10, 50, 16, "Click detected!");
        break;
    case TOUCH_GESTURE_SWIPE_UP:
        LCD_ShowStringSimple(10, 50, 16, "Swipe up!");
        break;
    // ... 其他手势处理
}
```

### 4. 横竖屏切换

```c
// 切换到竖屏
LCD_SetOrientation(0);
Touch_ReconfigureForLCD();

// 切换到横屏
LCD_SetOrientation(1);
Touch_ReconfigureForLCD();
```

## 测试和验证

### 运行基础测试

```c
#include "lcd_test.h"

// LCD基础功能测试
LCD_BasicTest();

// 触摸手势测试
LCD_TouchGestureTest();

// 横竖屏切换测试
LCD_TouchOrientationSwitchTest();
```

### 调试工具

```c
// 坐标转换调试
LCD_TouchTransformDebugTest();

// 坐标验证测试
LCD_TouchCoordinateTest();

// 性能测试
LCD_PerformanceTest();
```

## 配置选项

### 触摸检测参数

```c
#define TOUCH_EDGE_THRESHOLD_PX     5       // 边缘防误触阈值
#define TOUCH_MOVE_THRESHOLD_PX     10      // 移动检测阈值
#define TOUCH_SWIPE_THRESHOLD_PX    50      // 滑动检测阈值
#define TOUCH_LONG_PRESS_TIME_MS    800     // 长按时间阈值
```

### LCD显示参数

```c
#define LCD_PANEL_WIDTH             800     // 面板宽度
#define LCD_PANEL_HEIGHT            480     // 面板高度
#define LCD_DEFAULT_ORIENTATION     1       // 默认方向（横屏）
```

## 事件回调

系统提供了丰富的事件回调函数，用户可以重新实现：

```c
// 触摸事件
void Touch_OnPress(uint16_t x, uint16_t y);
void Touch_OnRelease(uint16_t x, uint16_t y);
void Touch_OnMove(uint16_t x, uint16_t y, int16_t delta_x, int16_t delta_y);

// 手势事件
void Touch_OnClick(uint16_t x, uint16_t y);
void Touch_OnLongPress(uint16_t x, uint16_t y, uint32_t duration_ms);
void Touch_OnSwipe(touch_gesture_type_t gesture_type, 
                   uint16_t start_x, uint16_t start_y, 
                   uint16_t end_x, uint16_t end_y);
```

## 故障排除

### 常见问题

1. **LCD显示异常**
   - 检查LTDC配置
   - 验证时钟设置
   - 确认显存配置

2. **触摸无响应**
   - 检查I2C连接
   - 验证中断配置
   - 确认电源供应

3. **坐标不准确**
   - 运行坐标调试测试
   - 检查屏幕方向配置
   - 验证坐标转换参数

### 调试步骤

1. **硬件检查**
   - 验证所有连接
   - 检查电源电压
   - 测试I2C通信

2. **软件调试**
   - 运行基础测试函数
   - 检查初始化返回值
   - 使用调试工具分析

3. **性能优化**
   - 调整检测参数
   - 优化中断处理
   - 减少不必要的计算

## 文档链接

- **[触摸驱动详细文档](README_Touch.md)** - GT1158/GT9xx触摸芯片驱动的完整说明
- **[测试函数文档](README_LCD_Test.md)** - LCD和触摸测试函数的详细使用指南

## 版本信息

- **当前版本**：V4.0
- **发布日期**：2025-01-27
- **主要更新**：
  - 支持横竖屏动态切换
  - 优化坐标转换算法
  - 增强防误触机制
  - 完善测试和调试功能

## 技术支持

### 获取帮助

1. **查看文档**：阅读相关README文件
2. **运行测试**：使用提供的测试函数诊断问题
3. **检查示例**：参考代码示例和最佳实践
4. **联系支持**：如需进一步帮助，请联系技术支持团队

### 贡献代码

欢迎提交Issue和Pull Request来改进这个项目：

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

---

**注意**：使用前请确保硬件连接正确，并根据实际硬件配置调整相关参数。
