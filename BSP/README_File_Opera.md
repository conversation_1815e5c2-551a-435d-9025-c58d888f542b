# File Opera Library - 文件操作库

## 概述

File Opera Library 是一个基于 FatFS 文件系统的高级文件操作库，为 STM32 项目提供简单易用的文件和目录操作接口。

## 功能特性

### 🗂️ **文件基本操作**
- 文件创建、删除、重命名
- 文件复制、移动
- 文件存在性检查
- 文件信息获取

### 📁 **目录操作**
- 目录创建、删除
- 目录存在性检查
- 目录内容列表

### 📖 **文件读写**
- 普通读写操作
- 4K对齐优化读写（提升Flash性能）
- 文件追加写入

### 🔄 **批量操作**
- 批量文件删除
- 支持通配符模式匹配
- 操作进度回调

### 💾 **磁盘管理**
- 磁盘信息查询
- 磁盘格式化
- 磁盘清理

## 快速开始

### 1. 包含头文件

```c
#include "file_opera.h"
```

### 2. 基本文件操作

```c
file_result_t result;

// 创建文件
result = File_Create("test.txt", "Hello World!");
if (result != FILE_OK) {
    printf("Error: %s\n", File_GetErrorString(result));
}

// 检查文件是否存在
uint8_t exists;
result = File_Exists("test.txt", &exists);
if (exists) {
    printf("File exists!\n");
}

// 复制文件
result = File_Copy("test.txt", "backup.txt");

// 移动文件
result = File_Move("backup.txt", "archive/backup.txt");

// 删除文件
result = File_Delete("test.txt");
```

### 3. 目录操作

```c
// 创建目录
result = Dir_Create("data");

// 列出目录内容
file_info_t files[20];
uint32_t count;
result = Dir_List("data", files, 20, &count);

for (uint32_t i = 0; i < count; i++) {
    printf("%s - %s (%d bytes)\n", 
           files[i].name,
           files[i].is_directory ? "DIR" : "FILE",
           files[i].size);
}
```

### 4. 文件读写

```c
// 写入数据
const char* data = "This is test data";
result = File_Write("data.txt", data, strlen(data));

// 读取数据
char buffer[100];
uint32_t bytes_read;
result = File_Read("data.txt", buffer, sizeof(buffer), &bytes_read);
buffer[bytes_read] = '\0';  // 添加字符串结束符
printf("Read: %s\n", buffer);

// 追加数据
result = File_Append("data.txt", "\nAppended line", 14);
```

### 5. 4K对齐优化读写

```c
// 4K对齐写入（优化Flash性能）
uint8_t large_data[4096];
memset(large_data, 0xAA, sizeof(large_data));
result = File_Write4K("large_file.bin", large_data, sizeof(large_data));

// 4K对齐读取
uint8_t read_buffer[4096];
uint32_t bytes_read;
result = File_Read4K("large_file.bin", read_buffer, sizeof(read_buffer), &bytes_read);
```

### 6. 磁盘信息

```c
disk_info_t disk;
result = Disk_GetInfo(&disk);
if (result == FILE_OK) {
    printf("Disk Info:\n");
    printf("  Total: %d KB\n", disk.total_size);
    printf("  Free:  %d KB\n", disk.free_size);
    printf("  Used:  %d KB (%d%%)\n", disk.used_size, disk.usage_percent);
    printf("  FAT Type: FAT%d\n", disk.fat_type);
}
```

### 7. 批量操作

```c
// 批量删除回调函数
void delete_callback(const char* filename, uint32_t index, uint32_t total, file_result_t result) {
    printf("Deleting %s (%d/%d): %s\n", 
           filename, index, total, 
           (result == FILE_OK) ? "OK" : "FAILED");
}

// 批量删除临时文件
result = File_BatchDelete("temp", "*.tmp", delete_callback);
```

## 错误处理

所有函数都返回 `file_result_t` 类型的结果：

```c
file_result_t result = File_Create("test.txt", NULL);
if (result != FILE_OK) {
    printf("Operation failed: %s\n", File_GetErrorString(result));
}
```

### 错误码说明

| 错误码 | 说明 |
|--------|------|
| `FILE_OK` | 操作成功 |
| `FILE_ERROR_INVALID_PARAM` | 无效参数 |
| `FILE_ERROR_FILE_NOT_FOUND` | 文件未找到 |
| `FILE_ERROR_DIR_NOT_FOUND` | 目录未找到 |
| `FILE_ERROR_FILE_EXISTS` | 文件已存在 |
| `FILE_ERROR_NO_SPACE` | 磁盘空间不足 |
| `FILE_ERROR_ACCESS_DENIED` | 访问被拒绝 |
| `FILE_ERROR_FATFS_ERROR` | FatFS底层错误 |

## 配置选项

在 `file_opera.h` 中可以配置以下参数：

```c
#define FILE_MAX_PATH_LEN           256     // 最大路径长度
#define FILE_MAX_NAME_LEN           64      // 最大文件名长度
#define FILE_BUFFER_SIZE            4096    // 默认缓冲区大小
#define FILE_4K_ALIGNED_SIZE        4096    // 4K对齐缓冲区大小
#define FILE_COPY_BUFFER_SIZE       8192    // 文件复制缓冲区大小
#define FILE_MAX_BATCH_COUNT        100     // 最大批量操作文件数量
```

## 性能优化建议

1. **使用4K对齐读写**：对于大文件操作，使用 `File_Write4K()` 和 `File_Read4K()` 可以提升Flash存储性能
2. **批量操作**：使用批量删除函数比逐个删除文件更高效
3. **缓冲区大小**：根据实际需求调整缓冲区大小配置

## 依赖项

- STM32 HAL库
- FatFS文件系统
- W25QXX Flash驱动（可选）

## 注意事项

1. 使用前确保FatFS已正确初始化
2. 文件路径使用Unix风格的斜杠分隔符 "/"
3. 4K对齐操作需要足够的RAM空间用于缓冲区
4. 批量操作时注意内存使用情况

## 版本历史

- **V1.0** (2025-08-02)
  - 初始版本
  - 支持基本文件和目录操作
  - 4K对齐优化读写
  - 批量操作和磁盘管理功能
