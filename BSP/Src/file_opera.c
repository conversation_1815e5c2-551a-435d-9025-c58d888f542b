/**
 ******************************************************************************
 * @file    file_opera.c
 * @brief   文件操作库实现文件 - 提供常用的FatFS文件系统操作函数
 * <AUTHOR> Project Team
 * @version V1.0
 * @date    2025-08-02
 ******************************************************************************
 * @attention
 *
 * 实现功能：
 * - 文件和目录的基本操作（创建、删除、复制、移动等）
 * - 4K对齐优化的读写操作，提升Flash存储性能
 * - 批量文件操作，支持模式匹配和回调函数
 * - 磁盘信息查询和管理功能
 * - 完善的错误处理和FatFS错误码转换
 *
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "file_opera.h"

/* Private variables ---------------------------------------------------------*/
static uint8_t file_buffer[FILE_BUFFER_SIZE] __attribute__((aligned(4)));
static uint8_t copy_buffer[FILE_COPY_BUFFER_SIZE] __attribute__((aligned(4)));

/* Private function prototypes -----------------------------------------------*/
static file_result_t validate_filename(const char* filename);
static file_result_t ensure_directory_exists(const char* path);
static uint8_t match_pattern(const char* filename, const char* pattern);

/* Exported functions --------------------------------------------------------*/

/**
 * @brief  将FatFS错误码转换为文件操作结果
 * @param  fr FatFS错误码
 * @retval file_result_t 文件操作结果
 */
file_result_t File_ConvertFatFSError(FRESULT fr)
{
    switch (fr) {
        case FR_OK:                 return FILE_OK;
        case FR_NO_FILE:           return FILE_ERROR_FILE_NOT_FOUND;
        case FR_NO_PATH:           return FILE_ERROR_DIR_NOT_FOUND;
        case FR_INVALID_NAME:      return FILE_ERROR_INVALID_PARAM;
        case FR_DENIED:            return FILE_ERROR_ACCESS_DENIED;
        case FR_EXIST:             return FILE_ERROR_FILE_EXISTS;
        case FR_DISK_ERR:          return FILE_ERROR_FATFS_ERROR;
        case FR_NOT_READY:         return FILE_ERROR_FATFS_ERROR;
        case FR_NO_FILESYSTEM:     return FILE_ERROR_FATFS_ERROR;
        default:                   return FILE_ERROR;
    }
}

/**
 * @brief  获取错误描述字符串
 * @param  result 文件操作结果
 * @retval const char* 错误描述字符串
 */
const char* File_GetErrorString(file_result_t result)
{
    switch (result) {
        case FILE_OK:                    return "Success";
        case FILE_ERROR:                 return "General error";
        case FILE_ERROR_INVALID_PARAM:   return "Invalid parameter";
        case FILE_ERROR_FILE_NOT_FOUND:  return "File not found";
        case FILE_ERROR_DIR_NOT_FOUND:   return "Directory not found";
        case FILE_ERROR_FILE_EXISTS:     return "File already exists";
        case FILE_ERROR_DIR_EXISTS:      return "Directory already exists";
        case FILE_ERROR_NO_SPACE:        return "No space left";
        case FILE_ERROR_ACCESS_DENIED:   return "Access denied";
        case FILE_ERROR_BUFFER_TOO_SMALL: return "Buffer too small";
        case FILE_ERROR_PATH_TOO_LONG:   return "Path too long";
        case FILE_ERROR_FATFS_ERROR:     return "FatFS error";
        default:                         return "Unknown error";
    }
}

/**
 * @brief  创建文件并写入初始数据
 * @param  filename 文件名
 * @param  initial_data 初始数据（可为NULL）
 * @retval file_result_t 操作结果
 */
file_result_t File_Create(const char* filename, const char* initial_data)
{
    FIL file;
    FRESULT fr;
    UINT bytes_written;
    
    if (!filename) {
        return FILE_ERROR_INVALID_PARAM;
    }
    
    file_result_t result = validate_filename(filename);
    if (result != FILE_OK) {
        return result;
    }
    
    // 创建文件
    fr = f_open(&file, filename, FA_CREATE_NEW | FA_WRITE);
    if (fr != FR_OK) {
        return File_ConvertFatFSError(fr);
    }
    
    // 写入初始数据
    if (initial_data) {
        fr = f_write(&file, initial_data, strlen(initial_data), &bytes_written);
        if (fr != FR_OK) {
            f_close(&file);
            return File_ConvertFatFSError(fr);
        }
    }
    
    f_close(&file);
    return FILE_OK;
}

/**
 * @brief  删除文件
 * @param  filename 文件名
 * @retval file_result_t 操作结果
 */
file_result_t File_Delete(const char* filename)
{
    FRESULT fr;
    
    if (!filename) {
        return FILE_ERROR_INVALID_PARAM;
    }
    
    fr = f_unlink(filename);
    return File_ConvertFatFSError(fr);
}

/**
 * @brief  重命名文件
 * @param  old_name 原文件名
 * @param  new_name 新文件名
 * @retval file_result_t 操作结果
 */
file_result_t File_Rename(const char* old_name, const char* new_name)
{
    FRESULT fr;
    
    if (!old_name || !new_name) {
        return FILE_ERROR_INVALID_PARAM;
    }
    
    file_result_t result = validate_filename(new_name);
    if (result != FILE_OK) {
        return result;
    }
    
    fr = f_rename(old_name, new_name);
    return File_ConvertFatFSError(fr);
}

/**
 * @brief  复制文件
 * @param  src_file 源文件名
 * @param  dst_file 目标文件名
 * @retval file_result_t 操作结果
 */
file_result_t File_Copy(const char* src_file, const char* dst_file)
{
    FIL src, dst;
    FRESULT fr;
    UINT bytes_read, bytes_written;
    
    if (!src_file || !dst_file) {
        return FILE_ERROR_INVALID_PARAM;
    }
    
    // 打开源文件
    fr = f_open(&src, src_file, FA_READ);
    if (fr != FR_OK) {
        return File_ConvertFatFSError(fr);
    }
    
    // 创建目标文件
    fr = f_open(&dst, dst_file, FA_CREATE_NEW | FA_WRITE);
    if (fr != FR_OK) {
        f_close(&src);
        return File_ConvertFatFSError(fr);
    }
    
    // 复制数据
    do {
        fr = f_read(&src, copy_buffer, sizeof(copy_buffer), &bytes_read);
        if (fr != FR_OK) break;
        
        if (bytes_read > 0) {
            fr = f_write(&dst, copy_buffer, bytes_read, &bytes_written);
            if (fr != FR_OK || bytes_written != bytes_read) break;
        }
    } while (bytes_read == sizeof(copy_buffer));
    
    f_close(&src);
    f_close(&dst);
    
    return File_ConvertFatFSError(fr);
}

/**
 * @brief  移动文件
 * @param  src_file 源文件名
 * @param  dst_file 目标文件名
 * @retval file_result_t 操作结果
 */
file_result_t File_Move(const char* src_file, const char* dst_file)
{
    FRESULT fr;
    
    if (!src_file || !dst_file) {
        return FILE_ERROR_INVALID_PARAM;
    }
    
    // 尝试直接重命名（如果在同一目录下）
    fr = f_rename(src_file, dst_file);
    if (fr == FR_OK) {
        return FILE_OK;
    }
    
    // 如果重命名失败，则复制后删除
    file_result_t result = File_Copy(src_file, dst_file);
    if (result == FILE_OK) {
        result = File_Delete(src_file);
    }
    
    return result;
}

/**
 * @brief  检查文件是否存在
 * @param  filename 文件名
 * @param  exists 存在标志指针
 * @retval file_result_t 操作结果
 */
file_result_t File_Exists(const char* filename, uint8_t* exists)
{
    FILINFO fno;
    FRESULT fr;
    
    if (!filename || !exists) {
        return FILE_ERROR_INVALID_PARAM;
    }
    
    fr = f_stat(filename, &fno);
    *exists = (fr == FR_OK) ? 1 : 0;
    
    return FILE_OK;
}

/**
 * @brief  创建目录
 * @param  dirname 目录名
 * @retval file_result_t 操作结果
 */
file_result_t Dir_Create(const char* dirname)
{
    FRESULT fr;
    
    if (!dirname) {
        return FILE_ERROR_INVALID_PARAM;
    }
    
    fr = f_mkdir(dirname);
    return File_ConvertFatFSError(fr);
}

/**
 * @brief  删除目录
 * @param  dirname 目录名
 * @retval file_result_t 操作结果
 */
file_result_t Dir_Delete(const char* dirname)
{
    FRESULT fr;
    
    if (!dirname) {
        return FILE_ERROR_INVALID_PARAM;
    }
    
    fr = f_unlink(dirname);
    return File_ConvertFatFSError(fr);
}

/**
 * @brief  检查目录是否存在
 * @param  dirname 目录名
 * @param  exists 存在标志指针
 * @retval file_result_t 操作结果
 */
file_result_t Dir_Exists(const char* dirname, uint8_t* exists)
{
    FILINFO fno;
    FRESULT fr;
    
    if (!dirname || !exists) {
        return FILE_ERROR_INVALID_PARAM;
    }
    
    fr = f_stat(dirname, &fno);
    *exists = (fr == FR_OK && (fno.fattrib & AM_DIR)) ? 1 : 0;
    
    return FILE_OK;
}

/**
 * @brief  列出目录中的文件
 * @param  dirname 目录名
 * @param  file_list 文件信息数组
 * @param  max_count 最大文件数量
 * @param  actual_count 实际文件数量指针
 * @retval file_result_t 操作结果
 */
file_result_t Dir_List(const char* dirname, file_info_t* file_list, uint32_t max_count, uint32_t* actual_count)
{
    DIR dir;
    FILINFO fno;
    FRESULT fr;
    uint32_t count = 0;

    if (!dirname || !file_list || !actual_count) {
        return FILE_ERROR_INVALID_PARAM;
    }

    *actual_count = 0;

    fr = f_opendir(&dir, dirname);
    if (fr != FR_OK) {
        return File_ConvertFatFSError(fr);
    }

    while (count < max_count) {
        fr = f_readdir(&dir, &fno);
        if (fr != FR_OK || fno.fname[0] == 0) break;

        // 跳过 "." 和 ".." 目录
        if (strcmp(fno.fname, ".") == 0 || strcmp(fno.fname, "..") == 0) {
            continue;
        }

        // 填充文件信息
        strncpy(file_list[count].name, fno.fname, FILE_MAX_NAME_LEN - 1);
        file_list[count].name[FILE_MAX_NAME_LEN - 1] = '\0';
        file_list[count].size = fno.fsize;
        file_list[count].date = fno.fdate;
        file_list[count].time = fno.ftime;
        file_list[count].attrib = fno.fattrib;
        file_list[count].is_directory = (fno.fattrib & AM_DIR) ? 1 : 0;

        count++;
    }

    f_closedir(&dir);
    *actual_count = count;

    return FILE_OK;
}

/**
 * @brief  写入数据到文件
 * @param  filename 文件名
 * @param  data 数据指针
 * @param  size 数据大小
 * @retval file_result_t 操作结果
 */
file_result_t File_Write(const char* filename, const void* data, uint32_t size)
{
    FIL file;
    FRESULT fr;
    UINT bytes_written;

    if (!filename || !data || size == 0) {
        return FILE_ERROR_INVALID_PARAM;
    }

    fr = f_open(&file, filename, FA_CREATE_ALWAYS | FA_WRITE);
    if (fr != FR_OK) {
        return File_ConvertFatFSError(fr);
    }

    fr = f_write(&file, data, size, &bytes_written);
    f_close(&file);

    if (fr != FR_OK || bytes_written != size) {
        return File_ConvertFatFSError(fr);
    }

    return FILE_OK;
}

/**
 * @brief  从文件读取数据
 * @param  filename 文件名
 * @param  buffer 缓冲区指针
 * @param  size 要读取的大小
 * @param  bytes_read 实际读取的字节数指针
 * @retval file_result_t 操作结果
 */
file_result_t File_Read(const char* filename, void* buffer, uint32_t size, uint32_t* bytes_read)
{
    FIL file;
    FRESULT fr;
    UINT read_count;

    if (!filename || !buffer || !bytes_read) {
        return FILE_ERROR_INVALID_PARAM;
    }

    *bytes_read = 0;

    fr = f_open(&file, filename, FA_READ);
    if (fr != FR_OK) {
        return File_ConvertFatFSError(fr);
    }

    fr = f_read(&file, buffer, size, &read_count);
    f_close(&file);

    if (fr != FR_OK) {
        return File_ConvertFatFSError(fr);
    }

    *bytes_read = read_count;
    return FILE_OK;
}

/**
 * @brief  追加数据到文件
 * @param  filename 文件名
 * @param  data 数据指针
 * @param  size 数据大小
 * @retval file_result_t 操作结果
 */
file_result_t File_Append(const char* filename, const void* data, uint32_t size)
{
    FIL file;
    FRESULT fr;
    UINT bytes_written;

    if (!filename || !data || size == 0) {
        return FILE_ERROR_INVALID_PARAM;
    }

    fr = f_open(&file, filename, FA_OPEN_APPEND | FA_WRITE);
    if (fr != FR_OK) {
        return File_ConvertFatFSError(fr);
    }

    fr = f_write(&file, data, size, &bytes_written);
    f_close(&file);

    if (fr != FR_OK || bytes_written != size) {
        return File_ConvertFatFSError(fr);
    }

    return FILE_OK;
}

/* Private functions ---------------------------------------------------------*/

/**
 * @brief  验证文件名有效性
 * @param  filename 文件名
 * @retval file_result_t 验证结果
 */
static file_result_t validate_filename(const char* filename)
{
    if (!filename || strlen(filename) == 0) {
        return FILE_ERROR_INVALID_PARAM;
    }

    if (strlen(filename) >= FILE_MAX_PATH_LEN) {
        return FILE_ERROR_PATH_TOO_LONG;
    }

    return FILE_OK;
}

/**
 * @brief  4K对齐写入数据到文件
 * @param  filename 文件名
 * @param  data 数据指针
 * @param  size 数据大小
 * @retval file_result_t 操作结果
 */
file_result_t File_Write4K(const char* filename, const void* data, uint32_t size)
{
    FIL file;
    FRESULT fr;
    UINT bytes_written;
    uint32_t aligned_size;

    if (!filename || !data || size == 0) {
        return FILE_ERROR_INVALID_PARAM;
    }

    // 计算4K对齐大小
    aligned_size = (size + 4095) & ~4095;

    fr = f_open(&file, filename, FA_CREATE_ALWAYS | FA_WRITE);
    if (fr != FR_OK) {
        return File_ConvertFatFSError(fr);
    }

    // 如果数据大小不是4K对齐，使用缓冲区
    if (size != aligned_size) {
        memset(file_buffer, 0, FILE_4K_ALIGNED_SIZE);
        memcpy(file_buffer, data, size);
        fr = f_write(&file, file_buffer, aligned_size, &bytes_written);
    } else {
        fr = f_write(&file, data, size, &bytes_written);
    }

    f_close(&file);

    if (fr != FR_OK) {
        return File_ConvertFatFSError(fr);
    }

    return FILE_OK;
}

/**
 * @brief  4K对齐从文件读取数据
 * @param  filename 文件名
 * @param  buffer 缓冲区指针
 * @param  size 要读取的大小
 * @param  bytes_read 实际读取的字节数指针
 * @retval file_result_t 操作结果
 */
file_result_t File_Read4K(const char* filename, void* buffer, uint32_t size, uint32_t* bytes_read)
{
    FIL file;
    FRESULT fr;
    UINT read_count;
    uint32_t aligned_size;

    if (!filename || !buffer || !bytes_read) {
        return FILE_ERROR_INVALID_PARAM;
    }

    *bytes_read = 0;

    // 计算4K对齐大小
    aligned_size = (size + 4095) & ~4095;

    fr = f_open(&file, filename, FA_READ);
    if (fr != FR_OK) {
        return File_ConvertFatFSError(fr);
    }

    // 如果请求大小不是4K对齐，使用缓冲区
    if (size != aligned_size) {
        fr = f_read(&file, file_buffer, aligned_size, &read_count);
        if (fr == FR_OK && read_count > 0) {
            uint32_t copy_size = (read_count < size) ? read_count : size;
            memcpy(buffer, file_buffer, copy_size);
            *bytes_read = copy_size;
        }
    } else {
        fr = f_read(&file, buffer, size, &read_count);
        *bytes_read = read_count;
    }

    f_close(&file);

    if (fr != FR_OK) {
        return File_ConvertFatFSError(fr);
    }

    return FILE_OK;
}

/**
 * @brief  获取文件信息
 * @param  filename 文件名
 * @param  info 文件信息结构体指针
 * @retval file_result_t 操作结果
 */
file_result_t File_GetInfo(const char* filename, file_info_t* info)
{
    FILINFO fno;
    FRESULT fr;

    if (!filename || !info) {
        return FILE_ERROR_INVALID_PARAM;
    }

    fr = f_stat(filename, &fno);
    if (fr != FR_OK) {
        return File_ConvertFatFSError(fr);
    }

    // 填充文件信息
    strncpy(info->name, fno.fname, FILE_MAX_NAME_LEN - 1);
    info->name[FILE_MAX_NAME_LEN - 1] = '\0';
    info->size = fno.fsize;
    info->date = fno.fdate;
    info->time = fno.ftime;
    info->attrib = fno.fattrib;
    info->is_directory = (fno.fattrib & AM_DIR) ? 1 : 0;

    return FILE_OK;
}

/**
 * @brief  获取文件大小
 * @param  filename 文件名
 * @param  size 文件大小指针
 * @retval file_result_t 操作结果
 */
file_result_t File_GetSize(const char* filename, uint32_t* size)
{
    FILINFO fno;
    FRESULT fr;

    if (!filename || !size) {
        return FILE_ERROR_INVALID_PARAM;
    }

    fr = f_stat(filename, &fno);
    if (fr != FR_OK) {
        return File_ConvertFatFSError(fr);
    }

    *size = fno.fsize;
    return FILE_OK;
}

/**
 * @brief  获取磁盘信息
 * @param  info 磁盘信息结构体指针
 * @retval file_result_t 操作结果
 */
file_result_t Disk_GetInfo(disk_info_t* info)
{
    FATFS *fs;
    DWORD fre_clust, fre_sect, tot_sect;
    FRESULT fr;

    if (!info) {
        return FILE_ERROR_INVALID_PARAM;
    }

    // 获取磁盘空间信息
    fr = f_getfree("", &fre_clust, &fs);
    if (fr != FR_OK) {
        return File_ConvertFatFSError(fr);
    }

    // 计算扇区数
    tot_sect = (fs->n_fatent - 2) * fs->csize;
    fre_sect = fre_clust * fs->csize;

    // 填充磁盘信息
    info->sector_size = FF_MAX_SS;
    info->cluster_size = fs->csize * FF_MAX_SS;
    info->total_size = tot_sect * FF_MAX_SS / 1024;  // KB
    info->free_size = fre_sect * FF_MAX_SS / 1024;   // KB
    info->used_size = info->total_size - info->free_size;
    info->usage_percent = (info->total_size > 0) ?
                         (info->used_size * 100 / info->total_size) : 0;

    // 确定FAT类型
    if (fs->fs_type == FS_FAT12) {
        info->fat_type = 12;
    } else if (fs->fs_type == FS_FAT16) {
        info->fat_type = 16;
    } else if (fs->fs_type == FS_FAT32) {
        info->fat_type = 32;
    } else {
        info->fat_type = 0;
    }

    return FILE_OK;
}

/**
 * @brief  格式化磁盘
 * @retval file_result_t 操作结果
 */
file_result_t Disk_Format(void)
{
    FRESULT fr;
    BYTE work[FF_MAX_SS];

    fr = f_mkfs("", FM_ANY, 0, work, sizeof(work));
    return File_ConvertFatFSError(fr);
}

/**
 * @brief  清理磁盘上的所有文件和目录
 * @retval file_result_t 操作结果
 */
file_result_t Disk_CleanAll(void)
{
    DIR dir;
    FILINFO fno;
    FRESULT fr;
    char path[FILE_MAX_PATH_LEN];

    fr = f_opendir(&dir, "");
    if (fr != FR_OK) {
        return File_ConvertFatFSError(fr);
    }

    while (1) {
        fr = f_readdir(&dir, &fno);
        if (fr != FR_OK || fno.fname[0] == 0) break;

        // 跳过 "." 和 ".." 目录
        if (strcmp(fno.fname, ".") == 0 || strcmp(fno.fname, "..") == 0) {
            continue;
        }

        // 构建完整路径
        snprintf(path, sizeof(path), "%s", fno.fname);

        // 删除文件或目录
        f_unlink(path);
    }

    f_closedir(&dir);
    return FILE_OK;
}

/**
 * @brief  批量删除文件
 * @param  dirname 目录名
 * @param  pattern 文件名模式（支持通配符*）
 * @param  callback 回调函数（可为NULL）
 * @retval file_result_t 操作结果
 */
file_result_t File_BatchDelete(const char* dirname, const char* pattern, file_batch_callback_t callback)
{
    DIR dir;
    FILINFO fno;
    FRESULT fr;
    char path[FILE_MAX_PATH_LEN];
    uint32_t count = 0;
    uint32_t deleted = 0;

    if (!dirname) {
        return FILE_ERROR_INVALID_PARAM;
    }

    fr = f_opendir(&dir, dirname);
    if (fr != FR_OK) {
        return File_ConvertFatFSError(fr);
    }

    // 首先计算匹配的文件数量
    while (1) {
        fr = f_readdir(&dir, &fno);
        if (fr != FR_OK || fno.fname[0] == 0) break;

        if (!(fno.fattrib & AM_DIR) && match_pattern(fno.fname, pattern)) {
            count++;
        }
    }

    // 重新打开目录进行删除
    f_closedir(&dir);
    fr = f_opendir(&dir, dirname);
    if (fr != FR_OK) {
        return File_ConvertFatFSError(fr);
    }

    while (1) {
        fr = f_readdir(&dir, &fno);
        if (fr != FR_OK || fno.fname[0] == 0) break;

        if (!(fno.fattrib & AM_DIR) && match_pattern(fno.fname, pattern)) {
            // 构建完整路径
            if (strlen(dirname) > 0) {
                snprintf(path, sizeof(path), "%s/%s", dirname, fno.fname);
            } else {
                snprintf(path, sizeof(path), "%s", fno.fname);
            }

            // 删除文件
            FRESULT del_result = f_unlink(path);
            file_result_t result = File_ConvertFatFSError(del_result);

            if (result == FILE_OK) {
                deleted++;
            }

            // 调用回调函数
            if (callback) {
                callback(fno.fname, deleted, count, result);
            }
        }
    }

    f_closedir(&dir);
    return FILE_OK;
}

/**
 * @brief  确保目录存在，如果不存在则创建
 * @param  path 目录路径
 * @retval file_result_t 操作结果
 */
static file_result_t ensure_directory_exists(const char* path)
{
    FILINFO fno;
    FRESULT fr;

    if (!path || strlen(path) == 0) {
        return FILE_OK;  // 根目录总是存在
    }

    fr = f_stat(path, &fno);
    if (fr == FR_OK && (fno.fattrib & AM_DIR)) {
        return FILE_OK;  // 目录已存在
    }

    // 目录不存在，创建它
    fr = f_mkdir(path);
    return File_ConvertFatFSError(fr);
}

/**
 * @brief  模式匹配函数（支持通配符*）
 * @param  filename 文件名
 * @param  pattern 模式字符串
 * @retval uint8_t 匹配结果（1-匹配，0-不匹配）
 */
static uint8_t match_pattern(const char* filename, const char* pattern)
{
    if (!filename || !pattern) {
        return 0;
    }

    // 如果模式为空或为"*"，匹配所有文件
    if (strlen(pattern) == 0 || strcmp(pattern, "*") == 0) {
        return 1;
    }

    // 简单的通配符匹配实现
    const char* p = pattern;
    const char* f = filename;

    while (*p && *f) {
        if (*p == '*') {
            // 跳过连续的*
            while (*p == '*') p++;

            // 如果*是最后一个字符，匹配剩余所有字符
            if (*p == '\0') return 1;

            // 查找下一个匹配字符
            while (*f && *f != *p) f++;

            if (*f == '\0') return 0;
        } else if (*p == *f) {
            p++;
            f++;
        } else {
            return 0;
        }
    }

    // 处理模式末尾的*
    while (*p == '*') p++;

    return (*p == '\0' && *f == '\0') ? 1 : 0;
}
