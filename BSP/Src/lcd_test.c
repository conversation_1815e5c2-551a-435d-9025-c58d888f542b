/**
 ******************************************************************************
 * @file    lcd_test.c
 * @brief   LCD显示驱动测试文件
 * @note    用于测试重构后的LCD驱动功能
 ******************************************************************************
 */

#include "lcd.h"
#include "touch.h"
#include "main.h"
#include <stdio.h>
#include <stdlib.h>

/**
 * @brief  LCD基础功能测试
 * @param  None
 * @retval None
 */
void LCD_BasicTest(void)
{
    // 初始化LCD
    if (LCD_Init() == LCD_OK) {
        // 使用新的LCD_SetColor函数设置颜色
        LCD_SetColor(LCD_COLOR_RED, LCD_COLOR_WHITE);  // 红色前景，白色背景

        // 清屏为白色
        LCD_Clear(LCD_COLOR_WHITE);

        // 绘制一些基本图形
        LCD_DrawPixel(100, 100, LCD_COLOR_RED);
        LCD_DrawLine(50, 50, 150, 150, LCD_COLOR_BLUE);
        LCD_DrawRectangle(200, 200, 300, 250, LCD_COLOR_GREEN);
        LCD_DrawCircle(400, 300, 50, LCD_COLOR_YELLOW);

        // 填充矩形
        LCD_FillRect(50, 300, 150, 350, LCD_COLOR_CYAN);

        // 显示文本（使用设置的前景色和背景色）
        LCD_ShowStringSimple(10, 10, 16, "LCD Test - Basic Functions");
        LCD_ShowNumber(10, 40, 12345, 5, 16);

        // 测试不同颜色组合
        LCD_SetColor(LCD_COLOR_BLUE, LCD_COLOR_YELLOW);  // 蓝色前景，黄色背景
        LCD_ShowStringSimple(10, 70, 16, "Blue on Yellow");

        LCD_SetColor(LCD_COLOR_WHITE, LCD_COLOR_BLACK);  // 白色前景，黑色背景
        LCD_ShowStringSimple(10, 100, 16, "White on Black");

        // 显示当前模式信息
        LCD_SetColor(LCD_COLOR_GREEN, LCD_COLOR_WHITE);  // 绿色前景，白色背景
        #ifdef LCD_MULTI_LAYER_ENABLE
        LCD_ShowStringSimple(10, 130, 16, "Multi-Layer Mode Enabled");
        #else
        LCD_ShowStringSimple(10, 130, 16, "Single-Layer Mode");
        #endif
    }
}

#ifdef LCD_MULTI_LAYER_ENABLE
/**
 * @brief  LCD多层功能测试
 * @param  None
 * @retval None
 */
void LCD_MultiLayerTest(void)
{
    // 初始化LCD
    if (LCD_Init() == LCD_OK) {
        // 清除两个层
        LCD_ClearLayer(LCD_LAYER_0, LCD_COLOR_WHITE);
        LCD_ClearLayer(LCD_LAYER_1, LCD_COLOR_BLACK);

        // 在层0绘制背景
        LCD_SwitchLayer(LCD_LAYER_0);
        LCD_SetColor(LCD_COLOR_WHITE, LCD_COLOR_BLUE);  // 白色文字，蓝色背景
        LCD_FillRect(0, 0, 400, 240, LCD_COLOR_BLUE);
        LCD_ShowStringSimple(10, 10, 16, "Background Layer 0");
        LCD_ShowStringSimple(10, 30, 16, "Blue Background");

        // 在层1绘制前景
        LCD_SwitchLayer(LCD_LAYER_1);
        LCD_SetColor(LCD_COLOR_YELLOW, LCD_COLOR_RED);  // 黄色文字，红色背景
        LCD_SetLayerAlpha(LCD_LAYER_1, 128);  // 50% 透明度
        LCD_FillRect(100, 100, 300, 200, LCD_COLOR_RED);
        LCD_ShowStringSimple(110, 110, 16, "Foreground Layer 1");
        LCD_ShowStringSimple(110, 130, 16, "50% Alpha Blend");

        // 启用两个层
        LCD_EnableLayer(LCD_LAYER_0, 1);
        LCD_EnableLayer(LCD_LAYER_1, 1);

        // 测试动态透明度变化
        HAL_Delay(2000);
        for (uint8_t alpha = 255; alpha > 0; alpha -= 15) {
            LCD_SetLayerAlpha(LCD_LAYER_1, alpha);
            HAL_Delay(100);
        }

        // 恢复透明度
        LCD_SetLayerAlpha(LCD_LAYER_1, 128);
    }
}
#endif

/**
 * @brief  LCD性能测试
 * @param  None
 * @retval None
 */
void LCD_PerformanceTest(void)
{
    uint32_t start_time, end_time;
    
    // 测试填充性能
    start_time = HAL_GetTick();
    for (int i = 0; i < 10; i++) {
        LCD_FillRect(0, 0, 799, 479, LCD_COLOR_RED + i * 100);
    }
    end_time = HAL_GetTick();
    
    // 显示性能结果
    LCD_Clear(LCD_COLOR_WHITE);
    LCD_SetForegroundColor(LCD_COLOR_BLACK);
    LCD_ShowStringSimple(10, 10, 16, "Performance Test Results:");
    LCD_ShowStringSimple(10, 30, 16, "Fill 10 screens time (ms):");
    LCD_ShowNumber(250, 30, end_time - start_time, 5, 16);
    
    // 测试像素绘制性能
    start_time = HAL_GetTick();
    for (int i = 0; i < 1000; i++) {
        LCD_DrawPixel(i % 800, (i / 800) % 480, LCD_COLOR_BLUE);
    }
    end_time = HAL_GetTick();
    
    LCD_ShowStringSimple(10, 50, 16, "Draw 1000 pixels time (ms):");
    LCD_ShowNumber(250, 50, end_time - start_time, 5, 16);
}

/**
 * @brief  LCD颜色测试
 * @param  None
 * @retval None
 */
void LCD_ColorTest(void)
{
    uint32_t colors[] = {
        LCD_COLOR_RED, LCD_COLOR_GREEN, LCD_COLOR_BLUE,
        LCD_COLOR_YELLOW, LCD_COLOR_CYAN, LCD_COLOR_MAGENTA,
        LCD_COLOR_WHITE, LCD_COLOR_BLACK
    };
    
    char* color_names[] = {
        "RED", "GREEN", "BLUE", "YELLOW", 
        "CYAN", "MAGENTA", "WHITE", "BLACK"
    };
    
    LCD_Clear(LCD_COLOR_WHITE);
    LCD_SetColor(LCD_COLOR_BLACK, LCD_COLOR_WHITE);  // 使用新的LCD_SetColor函数
    LCD_ShowStringSimple(10, 10, 16, "Color Test - LCD_SetColor()");

    for (int i = 0; i < 8; i++) {
        // 绘制颜色块
        LCD_FillRect(50, 50 + i * 90, 100, 130 + i * 90, colors[i]);

        // 根据背景颜色选择合适的文字颜色
        if (colors[i] == LCD_COLOR_BLACK || colors[i] == LCD_COLOR_BLUE || colors[i] == LCD_COLOR_RED) {
            LCD_SetColor(LCD_COLOR_WHITE, colors[i]);  // 深色背景用白色文字
        } else {
            LCD_SetColor(LCD_COLOR_BLACK, colors[i]);  // 浅色背景用黑色文字
        }

        // 显示颜色名称
        LCD_ShowStringSimple(110, 55 + i * 90, 12, color_names[i]);
    }

    // 恢复默认颜色
    LCD_SetColor(LCD_COLOR_BLACK, LCD_COLOR_WHITE);
}

/**
 * @brief  LCD综合测试
 * @param  None
 * @retval None
 */
void LCD_ComprehensiveTest(void)
{
    // 基础功能测试
    LCD_BasicTest();
    HAL_Delay(3000);
    
    // 颜色测试
    LCD_ColorTest();
    HAL_Delay(3000);
    
    // 性能测试
    LCD_PerformanceTest();
    HAL_Delay(3000);
    
    #ifdef LCD_MULTI_LAYER_ENABLE
    // 多层测试
    LCD_MultiLayerTest();
    HAL_Delay(3000);
    #endif
    
    // 测试完成
    LCD_Clear(LCD_COLOR_GREEN);
    LCD_SetColor(LCD_COLOR_WHITE, LCD_COLOR_GREEN);
    LCD_ShowStringSimple(300, 200, 24, "Test Complete!");
}

/**
 * @brief  LCD_SetColor函数专项测试
 * @param  None
 * @retval None
 */
void LCD_SetColorTest(void)
{
    LCD_Clear(LCD_COLOR_WHITE);

    // 测试标题
    LCD_SetColor(LCD_COLOR_BLUE, LCD_COLOR_WHITE);
    LCD_ShowStringSimple(10, 10, 16, "LCD_SetColor() Function Test");

    // 测试不同的颜色组合
    struct {
        uint32_t fg_color;
        uint32_t bg_color;
        char* description;
    } color_tests[] = {
        {LCD_COLOR_RED, LCD_COLOR_WHITE, "Red on White"},
        {LCD_COLOR_WHITE, LCD_COLOR_BLACK, "White on Black"},
        {LCD_COLOR_BLUE, LCD_COLOR_YELLOW, "Blue on Yellow"},
        {LCD_COLOR_GREEN, LCD_COLOR_MAGENTA, "Green on Magenta"},
        {LCD_COLOR_CYAN, LCD_COLOR_RED, "Cyan on Red"},
        {LCD_COLOR_BLACK, LCD_COLOR_CYAN, "Black on Cyan"}
    };

    for (int i = 0; i < 6; i++) {
        // 设置颜色组合
        LCD_SetColor(color_tests[i].fg_color, color_tests[i].bg_color);

        // 显示文本（非叠加模式，会显示背景色）
        LCD_ShowStringSimple(10, 40 + i * 25, 16, color_tests[i].description);

        // 在右侧显示叠加模式（只显示前景色）
        LCD_ShowChar(300, 40 + i * 25, 'O', 16, 1);  // 叠加模式
        LCD_ShowChar(320, 40 + i * 25, 'V', 16, 1);  // 叠加模式
    }

    // 恢复默认颜色
    LCD_SetColor(LCD_COLOR_BLACK, LCD_COLOR_WHITE);
    LCD_ShowStringSimple(10, 200, 12, "Left: Normal mode (with background)");
    LCD_ShowStringSimple(10, 220, 12, "Right: Overlay mode (no background)");
}

/* ========================================================================================
 * 触摸手势测试函数
 * ======================================================================================== */

/**
 * @brief  Touch_WaitForInput 手势测试函数
 * @note   测试Touch_WaitForInput支持的5种手势：单击、上滑、下滑、左滑、右滑
 * @param  None
 * @retval None
 */
void LCD_TouchGestureTest(void)
{
    touch_gesture_type_t gesture;
    uint32_t test_count = 0;
    uint32_t click_count = 0;
    uint32_t swipe_up_count = 0;
    uint32_t swipe_down_count = 0;
    uint32_t swipe_left_count = 0;
    uint32_t swipe_right_count = 0;
    char display_buffer[100];

    /* 初始化LCD显示 */
    LCD_Clear(LCD_COLOR_WHITE);
    LCD_SetColor(LCD_COLOR_BLACK, LCD_COLOR_WHITE);

    /* 显示测试标题 */
    LCD_ShowStringSimple(10, 10, 20, "Touch Gesture Test");
    LCD_ShowStringSimple(10, 35, 14, "5 Gestures: Click + 4 Swipes");

    /* 显示说明 */
    LCD_SetColor(LCD_COLOR_BLUE, LCD_COLOR_WHITE);
    LCD_ShowStringSimple(10, 55, 12, "Supported: Click, Swipe Up/Down/Left/Right");
    LCD_ShowStringSimple(10, 70, 12, "Long Press is ignored by Touch_WaitForInput");

    /* 显示操作说明 */
    LCD_SetColor(LCD_COLOR_DARK_GRAY, LCD_COLOR_WHITE);
    LCD_ShowStringSimple(10, 90, 10, "- Quick tap for Click");
    LCD_ShowStringSimple(10, 105, 10, "- Drag 50px+ for Swipe gestures");
    LCD_ShowStringSimple(10, 120, 10, "- Edge touches (5px) are filtered");

    while (1)
    {
        test_count++;

        /* 显示等待状态 */
        LCD_SetColor(LCD_COLOR_RED, LCD_COLOR_WHITE);
        LCD_ShowStringSimple(10, 140, 14, "Waiting for gesture...      ");

        /* 等待触摸输入，3秒超时 */
        gesture = Touch_WaitForInput(3000);

        /* 清除结果区域 */
        LCD_FillRect(10, 160, 780, 120, LCD_COLOR_WHITE);

        /* 统计和显示结果 */
        switch (gesture)
        {
            case TOUCH_GESTURE_CLICK:
                click_count++;
                LCD_SetColor(LCD_COLOR_GREEN, LCD_COLOR_WHITE);
                LCD_ShowStringSimple(10, 140, 14, "CLICK detected!             ");
                LCD_ShowStringSimple(10, 160, 16, "Result: SINGLE CLICK");
                sprintf(display_buffer, "Duration: %lums", g_gesture_event.duration_ms);
                LCD_ShowStringSimple(10, 180, 12, display_buffer);
                sprintf(display_buffer, "Position: (%d, %d)", g_gesture_event.x, g_gesture_event.y);
                LCD_ShowStringSimple(10, 195, 12, display_buffer);
                break;

            case TOUCH_GESTURE_SWIPE_UP:
                swipe_up_count++;
                LCD_SetColor(LCD_COLOR_CYAN, LCD_COLOR_WHITE);
                LCD_ShowStringSimple(10, 140, 14, "SWIPE UP detected!          ");
                LCD_ShowStringSimple(10, 160, 16, "Result: SWIPE UP");
                sprintf(display_buffer, "From (%d,%d) to (%d,%d)",
                       g_gesture_event.start_x, g_gesture_event.start_y,
                       g_gesture_event.end_x, g_gesture_event.end_y);
                LCD_ShowStringSimple(10, 180, 10, display_buffer);
                break;

            case TOUCH_GESTURE_SWIPE_DOWN:
                swipe_down_count++;
                LCD_SetColor(LCD_COLOR_CYAN, LCD_COLOR_WHITE);
                LCD_ShowStringSimple(10, 140, 14, "SWIPE DOWN detected!        ");
                LCD_ShowStringSimple(10, 160, 16, "Result: SWIPE DOWN");
                sprintf(display_buffer, "From (%d,%d) to (%d,%d)",
                       g_gesture_event.start_x, g_gesture_event.start_y,
                       g_gesture_event.end_x, g_gesture_event.end_y);
                LCD_ShowStringSimple(10, 180, 10, display_buffer);
                break;

            case TOUCH_GESTURE_SWIPE_LEFT:
                swipe_left_count++;
                LCD_SetColor(LCD_COLOR_BLUE, LCD_COLOR_WHITE);
                LCD_ShowStringSimple(10, 140, 14, "SWIPE LEFT detected!        ");
                LCD_ShowStringSimple(10, 160, 16, "Result: SWIPE LEFT");
                sprintf(display_buffer, "From (%d,%d) to (%d,%d)",
                       g_gesture_event.start_x, g_gesture_event.start_y,
                       g_gesture_event.end_x, g_gesture_event.end_y);
                LCD_ShowStringSimple(10, 180, 10, display_buffer);
                break;

            case TOUCH_GESTURE_SWIPE_RIGHT:
                swipe_right_count++;
                LCD_SetColor(LCD_COLOR_BLUE, LCD_COLOR_WHITE);
                LCD_ShowStringSimple(10, 140, 14, "SWIPE RIGHT detected!       ");
                LCD_ShowStringSimple(10, 160, 16, "Result: SWIPE RIGHT");
                sprintf(display_buffer, "From (%d,%d) to (%d,%d)",
                       g_gesture_event.start_x, g_gesture_event.start_y,
                       g_gesture_event.end_x, g_gesture_event.end_y);
                LCD_ShowStringSimple(10, 180, 10, display_buffer);
                break;

            case TOUCH_GESTURE_NONE:
                LCD_SetColor(LCD_COLOR_GRAY, LCD_COLOR_WHITE);
                LCD_ShowStringSimple(10, 140, 14, "TIMEOUT (no gesture)        ");
                LCD_ShowStringSimple(10, 160, 16, "Result: 3s TIMEOUT");
                break;

            default:
                LCD_SetColor(LCD_COLOR_RED, LCD_COLOR_WHITE);
                LCD_ShowStringSimple(10, 140, 14, "UNEXPECTED gesture!         ");
                sprintf(display_buffer, "Result: UNKNOWN (%d)", gesture);
                LCD_ShowStringSimple(10, 160, 16, display_buffer);
                break;
        }

        /* 显示统计信息 */
        LCD_SetColor(LCD_COLOR_BLACK, LCD_COLOR_WHITE);
        LCD_ShowStringSimple(10, 210, 12, "Statistics:");

        sprintf(display_buffer, "Total: %lu", test_count);
        LCD_ShowStringSimple(10, 225, 12, display_buffer);

        sprintf(display_buffer, "Click: %lu", click_count);
        LCD_ShowStringSimple(80, 225, 12, display_buffer);

        sprintf(display_buffer, "Up: %lu", swipe_up_count);
        LCD_ShowStringSimple(10, 240, 12, display_buffer);

        sprintf(display_buffer, "Down: %lu", swipe_down_count);
        LCD_ShowStringSimple(60, 240, 12, display_buffer);

        sprintf(display_buffer, "Left: %lu", swipe_left_count);
        LCD_ShowStringSimple(120, 240, 12, display_buffer);

        sprintf(display_buffer, "Right: %lu", swipe_right_count);
        LCD_ShowStringSimple(180, 240, 12, display_buffer);

        /* 显示配置信息 */
        LCD_SetColor(LCD_COLOR_DARK_GRAY, LCD_COLOR_WHITE);
        sprintf(display_buffer, "LCD: %dx%d, Orient=%d | Touch: %dx%d, Orient=%d",
               g_lcd_device.width, g_lcd_device.height, g_lcd_device.orientation,
               g_gt1158_config.screen_width, g_gt1158_config.screen_height, g_gt1158_config.orientation);
        LCD_ShowStringSimple(10, 260, 10, display_buffer);

        sprintf(display_buffer, "Anti-Mistouch: Edge=%dpx, Move=%dpx, Swipe=%dpx",
               TOUCH_EDGE_THRESHOLD_PX, TOUCH_MOVE_THRESHOLD_PX, TOUCH_SWIPE_THRESHOLD_PX);
        LCD_ShowStringSimple(10, 275, 10, display_buffer);

        /* 延时显示结果 */
        HAL_Delay(1000);
    }
}

/**
 * @brief  触摸坐标验证测试函数
 * @note   测试触摸坐标与LCD坐标的匹配性，验证坐标转换是否正确
 * @param  None
 * @retval None
 */
void LCD_TouchCoordinateTest(void)
{
    touch_gesture_type_t gesture;
    char display_buffer[100];
    uint16_t touch_x, touch_y;

    /* 初始化LCD显示 */
    LCD_Clear(LCD_COLOR_WHITE);
    LCD_SetColor(LCD_COLOR_BLACK, LCD_COLOR_WHITE);

    /* 显示测试标题 */
    LCD_ShowStringSimple(10, 10, 20, "Touch Coordinate Test");
    LCD_ShowStringSimple(10, 35, 14, "Verify Touch-LCD Coordinate Mapping");

    /* 显示当前配置信息 */
    LCD_SetColor(LCD_COLOR_BLUE, LCD_COLOR_WHITE);
    sprintf(display_buffer, "LCD: %dx%d, Orient=%d",
           g_lcd_device.width, g_lcd_device.height, g_lcd_device.orientation);
    LCD_ShowStringSimple(10, 55, 12, display_buffer);

    sprintf(display_buffer, "Touch: %dx%d, Orient=%d",
           g_gt1158_config.screen_width, g_gt1158_config.screen_height, g_gt1158_config.orientation);
    LCD_ShowStringSimple(10, 70, 12, display_buffer);

    /* 显示坐标系统说明 */
    LCD_SetColor(LCD_COLOR_DARK_GRAY, LCD_COLOR_WHITE);
    if (g_lcd_device.orientation == 1) {
        LCD_ShowStringSimple(10, 90, 10, "Landscape Mode: (0,0) at top-left");
        LCD_ShowStringSimple(10, 105, 10, "X: left->right, Y: top->bottom");
    } else {
        LCD_ShowStringSimple(10, 90, 10, "Portrait Mode: (0,0) at landscape bottom-left");
        LCD_ShowStringSimple(10, 105, 10, "X: bottom->top, Y: left->right");
    }

    /* 绘制坐标参考点 */
    LCD_SetColor(LCD_COLOR_RED, LCD_COLOR_WHITE);

    /* 绘制四个角的参考点 */
    LCD_FillRect(0, 0, 20, 20, LCD_COLOR_RED);           /* 左上角 */
    LCD_ShowStringSimple(25, 5, 10, "(0,0)");

    LCD_FillRect(g_lcd_device.width-20, 0, g_lcd_device.width, 20, LCD_COLOR_GREEN);  /* 右上角 */
    sprintf(display_buffer, "(%d,0)", g_lcd_device.width-1);
    LCD_ShowStringSimple(g_lcd_device.width-80, 5, 10, display_buffer);

    LCD_FillRect(0, g_lcd_device.height-20, 20, g_lcd_device.height, LCD_COLOR_BLUE); /* 左下角 */
    sprintf(display_buffer, "(0,%d)", g_lcd_device.height-1);
    LCD_ShowStringSimple(25, g_lcd_device.height-15, 10, display_buffer);

    LCD_FillRect(g_lcd_device.width-20, g_lcd_device.height-20,
                 g_lcd_device.width, g_lcd_device.height, LCD_COLOR_MAGENTA);  /* 右下角 */
    sprintf(display_buffer, "(%d,%d)", g_lcd_device.width-1, g_lcd_device.height-1);
    LCD_ShowStringSimple(g_lcd_device.width-100, g_lcd_device.height-15, 10, display_buffer);

    /* 绘制中心参考点 */
    uint16_t center_x = g_lcd_device.width / 2;
    uint16_t center_y = g_lcd_device.height / 2;
    LCD_FillRect(center_x-10, center_y-10, center_x+10, center_y+10, LCD_COLOR_YELLOW);
    sprintf(display_buffer, "(%d,%d)", center_x, center_y);
    LCD_ShowStringSimple(center_x+15, center_y-5, 10, display_buffer);

    /* 显示操作说明 */
    LCD_SetColor(LCD_COLOR_BLACK, LCD_COLOR_WHITE);
    LCD_ShowStringSimple(10, 120, 12, "Touch the colored squares to verify coordinates:");
    LCD_ShowStringSimple(10, 135, 10, "Red=Top-Left, Green=Top-Right, Blue=Bottom-Left");
    LCD_ShowStringSimple(10, 150, 10, "Magenta=Bottom-Right, Yellow=Center");

    while (1)
    {
        /* 显示等待状态 */
        LCD_SetColor(LCD_COLOR_RED, LCD_COLOR_WHITE);
        LCD_ShowStringSimple(10, 170, 14, "Touch a reference point...      ");

        /* 等待触摸输入 */
        gesture = Touch_WaitForInput(TOUCH_WAIT_ALWAYS);

        if (gesture == TOUCH_GESTURE_CLICK)
        {
            /* 获取触摸坐标 */
            touch_x = g_gesture_event.x;
            touch_y = g_gesture_event.y;

            /* 清除结果区域 */
            LCD_FillRect(10, 190, 780, 100, LCD_COLOR_WHITE);

            /* 显示触摸结果 */
            LCD_SetColor(LCD_COLOR_GREEN, LCD_COLOR_WHITE);
            LCD_ShowStringSimple(10, 170, 14, "Touch detected!                ");

            sprintf(display_buffer, "Touch Coordinate: (%d, %d)", touch_x, touch_y);
            LCD_ShowStringSimple(10, 190, 14, display_buffer);

            /* 在触摸位置绘制标记 */
            LCD_FillRect(touch_x-5, touch_y-5, touch_x+5, touch_y+5, LCD_COLOR_CYAN);

            /* 分析触摸位置 */
            LCD_SetColor(LCD_COLOR_BLACK, LCD_COLOR_WHITE);
            if (touch_x < 50 && touch_y < 50) {
                LCD_ShowStringSimple(10, 210, 12, "Analysis: Top-Left corner touched");
            } else if (touch_x > g_lcd_device.width-50 && touch_y < 50) {
                LCD_ShowStringSimple(10, 210, 12, "Analysis: Top-Right corner touched");
            } else if (touch_x < 50 && touch_y > g_lcd_device.height-50) {
                LCD_ShowStringSimple(10, 210, 12, "Analysis: Bottom-Left corner touched");
            } else if (touch_x > g_lcd_device.width-50 && touch_y > g_lcd_device.height-50) {
                LCD_ShowStringSimple(10, 210, 12, "Analysis: Bottom-Right corner touched");
            } else if (abs(touch_x - center_x) < 30 && abs(touch_y - center_y) < 30) {
                LCD_ShowStringSimple(10, 210, 12, "Analysis: Center area touched");
            } else {
                LCD_ShowStringSimple(10, 210, 12, "Analysis: Other area touched");
            }

            /* 显示坐标精度信息 */
            sprintf(display_buffer, "Expected vs Actual coordinate verification");
            LCD_ShowStringSimple(10, 230, 10, display_buffer);

            /* 延时显示结果 */
            HAL_Delay(2000);
        }
    }
}

/**
 * @brief  触摸坐标转换调试测试函数
 * @note   详细显示触摸原始坐标和转换后坐标，用于调试坐标转换逻辑
 * @param  None
 * @retval None
 */
void LCD_TouchTransformDebugTest(void)
{
    touch_gesture_type_t gesture;
    char display_buffer[100];
    uint16_t touch_x, touch_y;

    /* 初始化LCD显示 */
    LCD_Clear(LCD_COLOR_WHITE);
    LCD_SetColor(LCD_COLOR_BLACK, LCD_COLOR_WHITE);

    /* 显示测试标题 */
    LCD_ShowStringSimple(10, 10, 18, "Touch Transform Debug");
    LCD_ShowStringSimple(10, 30, 12, "Raw vs Transformed Coordinates");

    /* 显示当前配置信息 */
    LCD_SetColor(LCD_COLOR_BLUE, LCD_COLOR_WHITE);
    sprintf(display_buffer, "LCD: %dx%d, Orient=%d",
           g_lcd_device.width, g_lcd_device.height, g_lcd_device.orientation);
    LCD_ShowStringSimple(10, 50, 12, display_buffer);

    sprintf(display_buffer, "Touch: %dx%d, Orient=%d",
           g_gt1158_config.screen_width, g_gt1158_config.screen_height, g_gt1158_config.orientation);
    LCD_ShowStringSimple(10, 65, 12, display_buffer);

    /* 显示坐标系统说明 */
    LCD_SetColor(LCD_COLOR_DARK_GRAY, LCD_COLOR_WHITE);
    if (g_lcd_device.orientation == 1) {
        LCD_ShowStringSimple(10, 85, 10, "Landscape: (0,0) top-left, X->right, Y->down");
        LCD_ShowStringSimple(10, 100, 10, "Touch: No transform (orientation 0)");
    } else {
        LCD_ShowStringSimple(10, 85, 10, "Portrait: (0,0) at landscape bottom-left");
        LCD_ShowStringSimple(10, 100, 10, "X: bottom->top, Y: left->right");
        LCD_ShowStringSimple(10, 115, 10, "Touch transform: X=height-raw_Y-1, Y=raw_X");
    }

    /* 绘制坐标参考网格 */
    LCD_SetColor(LCD_COLOR_LIGHT_GRAY, LCD_COLOR_WHITE);

    /* 绘制垂直线 */
    for (int x = 0; x < g_lcd_device.width; x += 100) {
        for (int y = 0; y < g_lcd_device.height; y += 2) {
            LCD_DrawPixel(x, y, LCD_COLOR_LIGHT_GRAY);
        }
    }

    /* 绘制水平线 */
    for (int y = 0; y < g_lcd_device.height; y += 100) {
        for (int x = 0; x < g_lcd_device.width; x += 2) {
            LCD_DrawPixel(x, y, LCD_COLOR_LIGHT_GRAY);
        }
    }

    /* 标记重要坐标点 */
    LCD_SetColor(LCD_COLOR_RED, LCD_COLOR_WHITE);
    LCD_FillRect(0, 0, 10, 10, LCD_COLOR_RED);
    LCD_ShowStringSimple(15, 5, 8, "(0,0)");

    if (g_lcd_device.orientation == 0) {
        /* 竖屏模式：标记横屏状态下的左下角位置 */
        LCD_SetColor(LCD_COLOR_GREEN, LCD_COLOR_WHITE);
        LCD_ShowStringSimple(10, 130, 10, "Green dot shows landscape bottom-left origin");
        /* 在竖屏坐标系统中，这应该是(0,0)的位置 */
    }

    /* 显示操作说明 */
    LCD_SetColor(LCD_COLOR_BLACK, LCD_COLOR_WHITE);
    LCD_ShowStringSimple(10, 150, 12, "Touch anywhere to see coordinate transform:");

    while (1)
    {
        /* 显示等待状态 */
        LCD_SetColor(LCD_COLOR_RED, LCD_COLOR_WHITE);
        LCD_ShowStringSimple(10, 170, 14, "Touch screen...             ");

        /* 等待触摸输入 */
        gesture = Touch_WaitForInput(TOUCH_WAIT_ALWAYS);

        if (gesture == TOUCH_GESTURE_CLICK)
        {
            /* 获取转换后的坐标 */
            touch_x = g_gesture_event.x;
            touch_y = g_gesture_event.y;

            /* 清除结果区域 */
            LCD_FillRect(10, 190, 780, 150, LCD_COLOR_WHITE);

            /* 显示触摸结果 */
            LCD_SetColor(LCD_COLOR_GREEN, LCD_COLOR_WHITE);
            LCD_ShowStringSimple(10, 170, 14, "Touch detected!            ");

            /* 显示转换后的坐标 */
            sprintf(display_buffer, "Final Coordinate: (%d, %d)", touch_x, touch_y);
            LCD_ShowStringSimple(10, 190, 14, display_buffer);

            /* 在触摸位置绘制标记 */
            LCD_FillRect(touch_x-3, touch_y-3, touch_x+3, touch_y+3, LCD_COLOR_CYAN);
            sprintf(display_buffer, "(%d,%d)", touch_x, touch_y);
            LCD_ShowStringSimple(touch_x+5, touch_y-5, 8, display_buffer);

            /* 显示坐标分析 */
            LCD_SetColor(LCD_COLOR_BLACK, LCD_COLOR_WHITE);

            if (g_lcd_device.orientation == 1) {
                /* 横屏模式分析 */
                sprintf(display_buffer, "Landscape mode: Direct mapping");
                LCD_ShowStringSimple(10, 210, 12, display_buffer);

                sprintf(display_buffer, "Expected: X in [0,%d], Y in [0,%d]",
                       g_lcd_device.width-1, g_lcd_device.height-1);
                LCD_ShowStringSimple(10, 225, 10, display_buffer);
            } else {
                /* 竖屏模式分析 */
                sprintf(display_buffer, "Portrait mode: Custom transform applied");
                LCD_ShowStringSimple(10, 210, 12, display_buffer);

                sprintf(display_buffer, "Expected: X in [0,%d], Y in [0,%d]",
                       g_lcd_device.width-1, g_lcd_device.height-1);
                LCD_ShowStringSimple(10, 225, 10, display_buffer);

                /* 显示坐标系统验证 */
                if (touch_x < 50 && touch_y < 50) {
                    LCD_ShowStringSimple(10, 240, 10, "Near origin (0,0) - landscape bottom-left");
                } else if (touch_x > g_lcd_device.width-50 && touch_y < 50) {
                    LCD_ShowStringSimple(10, 240, 10, "Near top-right - landscape bottom-right");
                } else if (touch_x < 50 && touch_y > g_lcd_device.height-50) {
                    LCD_ShowStringSimple(10, 240, 10, "Near bottom-left - landscape top-left");
                } else if (touch_x > g_lcd_device.width-50 && touch_y > g_lcd_device.height-50) {
                    LCD_ShowStringSimple(10, 240, 10, "Near bottom-right - landscape top-right");
                }
            }

            /* 验证坐标范围 */
            if (touch_x >= 0 && touch_x < g_lcd_device.width &&
                touch_y >= 0 && touch_y < g_lcd_device.height) {
                LCD_SetColor(LCD_COLOR_GREEN, LCD_COLOR_WHITE);
                LCD_ShowStringSimple(10, 260, 10, "Coordinate range: VALID");
            } else {
                LCD_SetColor(LCD_COLOR_RED, LCD_COLOR_WHITE);
                LCD_ShowStringSimple(10, 260, 10, "Coordinate range: INVALID!");
            }

            /* 延时显示结果 */
            HAL_Delay(2000);
        }
    }
}

/**
 * @brief  横竖屏切换触摸测试函数
 * @note   测试触摸系统在横竖屏切换时的适应性
 * @param  None
 * @retval None
 */
void LCD_TouchOrientationSwitchTest(void)
{
    touch_gesture_type_t gesture;
    char display_buffer[100];
    uint16_t touch_x, touch_y;
    uint8_t current_orientation = 1;  /* 开始为横屏 */
    uint32_t switch_count = 0;

    /* 初始化为横屏模式 */
    LCD_SetOrientation(1);
    Touch_ReconfigureForLCD();

    while (1)
    {
        /* 清屏并显示当前状态 */
        LCD_Clear(LCD_COLOR_WHITE);
        LCD_SetColor(LCD_COLOR_BLACK, LCD_COLOR_WHITE);

        /* 显示测试标题 */
        LCD_ShowStringSimple(10, 10, 18, "Orientation Switch Test");

        /* 显示当前方向信息 */
        if (current_orientation == 1) {
            LCD_ShowStringSimple(10, 35, 14, "Current: LANDSCAPE (800x480)");
        } else {
            LCD_ShowStringSimple(10, 35, 14, "Current: PORTRAIT (480x800)");
        }

        /* 显示切换计数 */
        sprintf(display_buffer, "Switch Count: %lu", switch_count);
        LCD_ShowStringSimple(10, 55, 12, display_buffer);

        /* 获取并显示当前触摸配置 */
        uint16_t touch_width, touch_height;
        screen_orientation_t touch_orientation;
        if (Touch_GetScreenInfo(&touch_width, &touch_height, &touch_orientation) == TOUCH_RESULT_OK)
        {
            sprintf(display_buffer, "Touch Config: %dx%d, Orient=%d",
                   touch_width, touch_height, touch_orientation);
            LCD_ShowStringSimple(10, 75, 12, display_buffer);
        }

        /* 显示LCD配置 */
        sprintf(display_buffer, "LCD Config: %dx%d, Orient=%d",
               g_lcd_device.width, g_lcd_device.height, g_lcd_device.orientation);
        LCD_ShowStringSimple(10, 95, 12, display_buffer);

        /* 绘制参考坐标点 */
        LCD_SetColor(LCD_COLOR_RED, LCD_COLOR_WHITE);
        LCD_FillRect(0, 0, 15, 15, LCD_COLOR_RED);
        LCD_ShowStringSimple(20, 5, 10, "(0,0)");

        LCD_SetColor(LCD_COLOR_GREEN, LCD_COLOR_WHITE);
        LCD_FillRect(g_lcd_device.width-15, 0, g_lcd_device.width, 15, LCD_COLOR_GREEN);
        sprintf(display_buffer, "(%d,0)", g_lcd_device.width-1);
        LCD_ShowStringSimple(g_lcd_device.width-80, 5, 10, display_buffer);

        LCD_SetColor(LCD_COLOR_BLUE, LCD_COLOR_WHITE);
        LCD_FillRect(0, g_lcd_device.height-15, 15, g_lcd_device.height, LCD_COLOR_BLUE);
        sprintf(display_buffer, "(0,%d)", g_lcd_device.height-1);
        LCD_ShowStringSimple(20, g_lcd_device.height-10, 10, display_buffer);

        LCD_SetColor(LCD_COLOR_MAGENTA, LCD_COLOR_WHITE);
        LCD_FillRect(g_lcd_device.width-15, g_lcd_device.height-15,
                     g_lcd_device.width, g_lcd_device.height, LCD_COLOR_MAGENTA);
        sprintf(display_buffer, "(%d,%d)", g_lcd_device.width-1, g_lcd_device.height-1);
        LCD_ShowStringSimple(g_lcd_device.width-100, g_lcd_device.height-10, 10, display_buffer);

        /* 显示操作说明 */
        LCD_SetColor(LCD_COLOR_BLACK, LCD_COLOR_WHITE);
        LCD_ShowStringSimple(10, 120, 12, "Touch anywhere to test coordinates");
        LCD_ShowStringSimple(10, 135, 12, "Touch center area to switch orientation");

        /* 绘制中心切换区域 */
        uint16_t center_x = g_lcd_device.width / 2;
        uint16_t center_y = g_lcd_device.height / 2;
        LCD_SetColor(LCD_COLOR_YELLOW, LCD_COLOR_WHITE);
        LCD_FillRect(center_x-50, center_y-25, center_x+50, center_y+25, LCD_COLOR_YELLOW);
        LCD_SetColor(LCD_COLOR_BLACK, LCD_COLOR_WHITE);
        LCD_ShowStringSimple(center_x-30, center_y-5, 12, "SWITCH");

        /* 等待触摸输入 */
        LCD_SetColor(LCD_COLOR_RED, LCD_COLOR_WHITE);
        LCD_ShowStringSimple(10, 155, 14, "Waiting for touch...        ");

        gesture = Touch_WaitForInput(TOUCH_WAIT_ALWAYS);

        if (gesture == TOUCH_GESTURE_CLICK)
        {
            touch_x = g_gesture_event.x;
            touch_y = g_gesture_event.y;

            /* 检查是否点击了切换区域 */
            if (touch_x >= center_x-50 && touch_x <= center_x+50 &&
                touch_y >= center_y-25 && touch_y <= center_y+25)
            {
                /* 切换屏幕方向 */
                current_orientation = (current_orientation == 1) ? 0 : 1;
                switch_count++;

                /* 应用新的方向配置 */
                LCD_SetOrientation(current_orientation);
                Touch_ReconfigureForLCD();

                /* 显示切换信息 */
                LCD_SetColor(LCD_COLOR_GREEN, LCD_COLOR_WHITE);
                LCD_ShowStringSimple(10, 155, 14, "Orientation switched!       ");
                HAL_Delay(1000);

                /* 继续下一轮测试 */
                continue;
            }
            else
            {
                /* 显示触摸坐标 */
                LCD_SetColor(LCD_COLOR_GREEN, LCD_COLOR_WHITE);
                LCD_ShowStringSimple(10, 155, 14, "Touch detected!             ");

                sprintf(display_buffer, "Touch Coordinate: (%d, %d)", touch_x, touch_y);
                LCD_ShowStringSimple(10, 175, 14, display_buffer);

                /* 在触摸位置绘制标记 */
                LCD_FillRect(touch_x-3, touch_y-3, touch_x+3, touch_y+3, LCD_COLOR_CYAN);

                /* 验证坐标范围 */
                if (Touch_IsCoordinateInRange(touch_x, touch_y))
                {
                    LCD_SetColor(LCD_COLOR_GREEN, LCD_COLOR_WHITE);
                    LCD_ShowStringSimple(10, 195, 12, "Coordinate validation: PASS");
                }
                else
                {
                    LCD_SetColor(LCD_COLOR_RED, LCD_COLOR_WHITE);
                    LCD_ShowStringSimple(10, 195, 12, "Coordinate validation: FAIL");
                }

                /* 延时显示结果 */
                HAL_Delay(2000);
            }
        }
    }
}

/**
 * @brief  检查LTDC Alpha值测试函数
 * @note   显示LCD_Init()后的LTDC层Alpha配置值
 * @param  None
 * @retval None
 */
void LCD_CheckAlphaValueTest(void)
{
    char display_buffer[100];
    extern LTDC_HandleTypeDef hltdc;

    /* 初始化LCD显示 */
    LCD_Clear(LCD_COLOR_WHITE);
    LCD_SetColor(LCD_COLOR_BLACK, LCD_COLOR_WHITE);

    /* 显示测试标题 */
    LCD_ShowStringSimple(10, 10, 20, "LTDC Alpha Value Check");
    LCD_ShowStringSimple(10, 35, 14, "After LCD_Init() and LTDC_ParameterInit()");

    /* 显示Layer 0的Alpha值 */
    LCD_SetColor(LCD_COLOR_BLUE, LCD_COLOR_WHITE);
    sprintf(display_buffer, "Layer 0 Alpha: %lu", hltdc.LayerCfg[0].Alpha);
    LCD_ShowStringSimple(10, 60, 16, display_buffer);

    sprintf(display_buffer, "Layer 0 Alpha0: %lu", hltdc.LayerCfg[0].Alpha0);
    LCD_ShowStringSimple(10, 80, 16, display_buffer);

    /* 显示Layer 0的其他配置 */
    LCD_SetColor(LCD_COLOR_DARK_GRAY, LCD_COLOR_WHITE);
    sprintf(display_buffer, "Layer 0 BlendingFactor1: 0x%lX", hltdc.LayerCfg[0].BlendingFactor1);
    LCD_ShowStringSimple(10, 100, 12, display_buffer);

    sprintf(display_buffer, "Layer 0 BlendingFactor2: 0x%lX", hltdc.LayerCfg[0].BlendingFactor2);
    LCD_ShowStringSimple(10, 115, 12, display_buffer);

    sprintf(display_buffer, "Layer 0 PixelFormat: 0x%lX", hltdc.LayerCfg[0].PixelFormat);
    LCD_ShowStringSimple(10, 130, 12, display_buffer);

    sprintf(display_buffer, "Layer 0 WindowX0: %lu, WindowX1: %lu",
           hltdc.LayerCfg[0].WindowX0, hltdc.LayerCfg[0].WindowX1);
    LCD_ShowStringSimple(10, 145, 12, display_buffer);

    sprintf(display_buffer, "Layer 0 WindowY0: %lu, WindowY1: %lu",
           hltdc.LayerCfg[0].WindowY0, hltdc.LayerCfg[0].WindowY1);
    LCD_ShowStringSimple(10, 160, 12, display_buffer);

    sprintf(display_buffer, "Layer 0 FBStartAdress: 0x%08lX", hltdc.LayerCfg[0].FBStartAdress);
    LCD_ShowStringSimple(10, 175, 12, display_buffer);

    /* 检查是否有Layer 1 */
    LCD_SetColor(LCD_COLOR_GREEN, LCD_COLOR_WHITE);
    LCD_ShowStringSimple(10, 200, 16, "Layer 1 Configuration:");

    sprintf(display_buffer, "Layer 1 Alpha: %lu", hltdc.LayerCfg[1].Alpha);
    LCD_ShowStringSimple(10, 220, 16, display_buffer);

    sprintf(display_buffer, "Layer 1 Alpha0: %lu", hltdc.LayerCfg[1].Alpha0);
    LCD_ShowStringSimple(10, 240, 16, display_buffer);

    /* 显示LTDC寄存器的实际值 */
    LCD_SetColor(LCD_COLOR_RED, LCD_COLOR_WHITE);
    LCD_ShowStringSimple(10, 270, 14, "LTDC Register Values:");

    /* 读取Layer 0的CACR寄存器（Constant Alpha Configuration Register） */
    uint32_t layer0_cacr = LTDC_LAYER(&hltdc, 0)->CACR;
    sprintf(display_buffer, "Layer 0 CACR Register: 0x%08lX", layer0_cacr);
    LCD_ShowStringSimple(10, 290, 12, display_buffer);

    sprintf(display_buffer, "Layer 0 Alpha from Register: %lu", layer0_cacr & 0xFF);
    LCD_ShowStringSimple(10, 305, 12, display_buffer);

    /* 读取Layer 1的CACR寄存器 */
    uint32_t layer1_cacr = LTDC_LAYER(&hltdc, 1)->CACR;
    sprintf(display_buffer, "Layer 1 CACR Register: 0x%08lX", layer1_cacr);
    LCD_ShowStringSimple(10, 320, 12, display_buffer);

    sprintf(display_buffer, "Layer 1 Alpha from Register: %lu", layer1_cacr & 0xFF);
    LCD_ShowStringSimple(10, 335, 12, display_buffer);

    /* 显示说明 */
    LCD_SetColor(LCD_COLOR_DARK_GRAY, LCD_COLOR_WHITE);
    LCD_ShowStringSimple(10, 360, 10, "Alpha Range: 0-255 (0=transparent, 255=opaque)");
    LCD_ShowStringSimple(10, 375, 10, "Initial config in ltdc.c: pLayerCfg.Alpha = 255");
    LCD_ShowStringSimple(10, 390, 10, "LTDC_ParameterInit() sets Alpha = 255");

    /* 等待用户操作 */
    LCD_SetColor(LCD_COLOR_BLACK, LCD_COLOR_WHITE);
    LCD_ShowStringSimple(10, 420, 14, "Touch screen to continue...");

    /* 等待触摸输入 */
    Touch_WaitForInput(TOUCH_WAIT_ALWAYS);
}

/**
 * @brief  LTDC Alpha值动态测试函数
 * @note   动态调整Alpha值并观察效果
 * @param  None
 * @retval None
 */
void LCD_AlphaDynamicTest(void)
{
    char display_buffer[100];
    extern LTDC_HandleTypeDef hltdc;
    uint8_t alpha_value = 255;
    touch_gesture_type_t gesture;

    /* 初始化LCD显示 */
    LCD_Clear(LCD_COLOR_WHITE);
    LCD_SetColor(LCD_COLOR_BLACK, LCD_COLOR_WHITE);

    /* 显示测试标题 */
    LCD_ShowStringSimple(10, 10, 20, "LTDC Alpha Dynamic Test");
    LCD_ShowStringSimple(10, 35, 14, "Touch to change Alpha value");

    /* 绘制一些背景图案 */
    LCD_FillRect(100, 100, 300, 200, LCD_COLOR_RED);
    LCD_FillRect(150, 150, 350, 250, LCD_COLOR_GREEN);
    LCD_FillRect(200, 200, 400, 300, LCD_COLOR_BLUE);

    while (1)
    {
        /* 显示当前Alpha值 */
        LCD_FillRect(10, 60, 400, 90, LCD_COLOR_WHITE);
        LCD_SetColor(LCD_COLOR_BLACK, LCD_COLOR_WHITE);
        sprintf(display_buffer, "Current Alpha: %d (0=transparent, 255=opaque)", alpha_value);
        LCD_ShowStringSimple(10, 60, 16, display_buffer);

        /* 显示操作说明 */
        LCD_ShowStringSimple(10, 320, 12, "Touch left: Alpha -= 25");
        LCD_ShowStringSimple(10, 335, 12, "Touch right: Alpha += 25");
        LCD_ShowStringSimple(10, 350, 12, "Touch center: Reset to 255");

        /* 应用当前Alpha值 */
        HAL_LTDC_SetAlpha(&hltdc, alpha_value, LCD_DEFAULT_LAYER);

        /* 等待触摸输入 */
        gesture = Touch_WaitForInput(TOUCH_WAIT_ALWAYS);

        if (gesture == TOUCH_GESTURE_CLICK)
        {
            uint16_t touch_x = g_gesture_event.x;
            uint16_t touch_y = g_gesture_event.y;

            if (touch_x < g_lcd_device.width / 3)
            {
                /* 左侧：减少Alpha */
                if (alpha_value >= 25)
                    alpha_value -= 25;
                else
                    alpha_value = 0;
            }
            else if (touch_x > (g_lcd_device.width * 2) / 3)
            {
                /* 右侧：增加Alpha */
                if (alpha_value <= 230)
                    alpha_value += 25;
                else
                    alpha_value = 255;
            }
            else
            {
                /* 中间：重置为255 */
                alpha_value = 255;
            }
        }
        else if (gesture == TOUCH_GESTURE_SWIPE_LEFT)
        {
            /* 左滑：大幅减少Alpha */
            if (alpha_value >= 50)
                alpha_value -= 50;
            else
                alpha_value = 0;
        }
        else if (gesture == TOUCH_GESTURE_SWIPE_RIGHT)
        {
            /* 右滑：大幅增加Alpha */
            if (alpha_value <= 205)
                alpha_value += 50;
            else
                alpha_value = 255;
        }
        else if (gesture == TOUCH_GESTURE_SWIPE_UP)
        {
            /* 上滑：设置为最大 */
            alpha_value = 255;
        }
        else if (gesture == TOUCH_GESTURE_SWIPE_DOWN)
        {
            /* 下滑：设置为最小 */
            alpha_value = 0;
        }
    }
}
