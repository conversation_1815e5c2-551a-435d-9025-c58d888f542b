//
// Created by wang on 2025/7/14.
//
#include "keyled.h"

KEYS ScanPressedKey(uint32_t timeout)
{
    uint32_t tickstart = HAL_GetTick();
    const uint32_t btnDelay = 20;

    while (1)
    {
        // KEY1
#ifdef KEY1_Pin
        if (HAL_GPIO_ReadPin(KEY1_GPIO_Port, KEY1_Pin) == GPIO_PIN_SET)
        {
            HAL_Delay(btnDelay);
            while (HAL_GPIO_ReadPin(KEY1_GPIO_Port, KEY1_Pin) == GPIO_PIN_SET){}
                return KEY_1;
        }
#endif

        // KEY2
#ifdef KEY2_Pin
        if (HAL_GPIO_ReadPin(KEY2_GPIO_Port, KEY2_Pin) == GPIO_PIN_SET)
        {
            HAL_Delay(btnDelay);
            while (HAL_GPIO_ReadPin(KEY2_GPIO_Port, KEY2_Pin) == GPIO_PIN_SET){}
                return KEY_2;
        }
#endif

        // KEY3
#ifdef KEY3_Pin
        if (HAL_GPIO_ReadPin(KEY3_GPIO_Port, KEY3_Pin) == GPIO_PIN_RESET)
        {
            HAL_Delay(btnDelay);
            while(HAL_GPIO_ReadPin(KEY3_GPIO_Port, KEY3_Pin) == GPIO_PIN_RESET){}
                return KEY_3;
        }
#endif

        // KEY4
#ifdef KEY4_Pin
        if (HAL_GPIO_ReadPin(KEY4_GPIO_Port, KEY4_Pin) == GPIO_PIN_RESET)
        {
            HAL_Delay(btnDelay);
            while (HAL_GPIO_ReadPin(KEY4_GPIO_Port, KEY4_Pin) == GPIO_PIN_RESET){}
                return KEY_4;
        }
#endif

        // 超时判断
        if (timeout != KEY_WAIT_ALWAYS)
        {
            if ((HAL_GetTick() - tickstart) > timeout)
                break;
        }
    }

    return KEY_NONE;
}

/*按键扫描实现 KEY_WAIT_ALWAYS为超时时间，0是一直等待，1000为等待1秒
KEYS curKey = ScanPressedKey(KEY_WAIT_ALWAYS);
switch (curKey)
{
case KEY_1:
LED_R_Toggle();
break;
case KEY_2:
LED_G_Toggle();
break;
default:
break;
}
HAL_Delay(200);
 */