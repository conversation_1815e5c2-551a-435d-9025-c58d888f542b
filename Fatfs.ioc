#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
CORTEX_M7.CPU_DCache=Enabled
CORTEX_M7.CPU_ICache=Enabled
CORTEX_M7.IPParameters=default_mode_Activation,CPU_ICache,CPU_DCache
CORTEX_M7.default_mode_Activation=0
DMA2D.ColorMode=DMA2D_OUTPUT_RGB565
DMA2D.IPParameters=ColorMode
FATFS.IPParameters=_CODE_PAGE,_MAX_SS
FATFS._CODE_PAGE=936
FATFS._MAX_SS=4096
FMC.CASLatency2=FMC_SDRAM_CAS_LATENCY_3
FMC.ColumnBitsNumber2=FMC_SDRAM_COLUMN_BITS_NUM_9
FMC.ExitSelfRefreshDelay2=9
FMC.IPParameters=ColumnBitsNumber2,CASLatency2,SDClockPeriod2,SDClockPeriod1,ReadPipeDelay2,ReadPipeDelay1,ReadBurst2,Read<PERSON>urst1,LoadToActiveDelay2,ExitSelfRefreshDelay2,SelfRefreshTime2,RowCycleDelay2,RowCycleDelay1,WriteRecoveryTime2,RPDelay2,RPDelay1,RCDDelay2
FMC.LoadToActiveDelay2=2
FMC.RCDDelay2=3
FMC.RPDelay1=3
FMC.RPDelay2=3
FMC.ReadBurst1=FMC_SDRAM_RBURST_ENABLE
FMC.ReadBurst2=FMC_SDRAM_RBURST_ENABLE
FMC.ReadPipeDelay1=FMC_SDRAM_RPIPE_DELAY_1
FMC.ReadPipeDelay2=FMC_SDRAM_RPIPE_DELAY_1
FMC.RowCycleDelay1=8
FMC.RowCycleDelay2=8
FMC.SDClockPeriod1=FMC_SDRAM_CLOCK_PERIOD_2
FMC.SDClockPeriod2=FMC_SDRAM_CLOCK_PERIOD_2
FMC.SelfRefreshTime2=6
FMC.WriteRecoveryTime2=3
File.Version=6
GPIO.groupedBy=Group By Peripherals
I2C2.I2C_Speed_Mode=I2C_Fast
I2C2.IPParameters=Timing,I2C_Speed_Mode
I2C2.Timing=0x6000030D
KeepUserPlacement=false
LTDC.ActiveW=800
LTDC.Alpha_L0=255
LTDC.FBStartAdress_L0=0xD0000000
LTDC.HBP=46
LTDC.HFP=40
LTDC.HSync=1
LTDC.IPParameters=HSync,HBP,ActiveW,HFP,VSync,VBP,VFP,Layers,PixelFormat_L0,WindowX1_L0,WindowY1_L0,Alpha_L0,FBStartAdress_L0,ImageWidth_L0,ImageHeight_L0
LTDC.ImageHeight_L0=480
LTDC.ImageWidth_L0=800
LTDC.Layers=0
LTDC.PixelFormat_L0=LTDC_PIXEL_FORMAT_RGB565
LTDC.VBP=23
LTDC.VFP=13
LTDC.VSync=3
LTDC.WindowX1_L0=800
LTDC.WindowY1_L0=480
Mcu.CPN=STM32F767IGT6
Mcu.Family=STM32F7
Mcu.IP0=CORTEX_M7
Mcu.IP1=DMA2D
Mcu.IP10=SYS
Mcu.IP2=FATFS
Mcu.IP3=FMC
Mcu.IP4=I2C2
Mcu.IP5=LTDC
Mcu.IP6=NVIC
Mcu.IP7=QUADSPI
Mcu.IP8=RCC
Mcu.IP9=RTC
Mcu.IPNb=11
Mcu.Name=STM32F767I(G-I)Tx
Mcu.Package=LQFP176
Mcu.Pin0=PI8
Mcu.Pin1=PC14/OSC32_IN
Mcu.Pin10=PF5
Mcu.Pin11=PF6
Mcu.Pin12=PF7
Mcu.Pin13=PF8
Mcu.Pin14=PF9
Mcu.Pin15=PF10
Mcu.Pin16=PH0/OSC_IN
Mcu.Pin17=PH1/OSC_OUT
Mcu.Pin18=PC0
Mcu.Pin19=PH4
Mcu.Pin2=PC15/OSC32_OUT
Mcu.Pin20=PH5
Mcu.Pin21=PA3
Mcu.Pin22=PB0
Mcu.Pin23=PB1
Mcu.Pin24=PB2
Mcu.Pin25=PF11
Mcu.Pin26=PF12
Mcu.Pin27=PF13
Mcu.Pin28=PF14
Mcu.Pin29=PF15
Mcu.Pin3=PI9
Mcu.Pin30=PG0
Mcu.Pin31=PG1
Mcu.Pin32=PE7
Mcu.Pin33=PE8
Mcu.Pin34=PE9
Mcu.Pin35=PE10
Mcu.Pin36=PE11
Mcu.Pin37=PE12
Mcu.Pin38=PE13
Mcu.Pin39=PE14
Mcu.Pin4=PI10
Mcu.Pin40=PE15
Mcu.Pin41=PB10
Mcu.Pin42=PH6
Mcu.Pin43=PH7
Mcu.Pin44=PD8
Mcu.Pin45=PD9
Mcu.Pin46=PD10
Mcu.Pin47=PD13
Mcu.Pin48=PD14
Mcu.Pin49=PD15
Mcu.Pin5=PF0
Mcu.Pin50=PG2
Mcu.Pin51=PG4
Mcu.Pin52=PG5
Mcu.Pin53=PG6
Mcu.Pin54=PG7
Mcu.Pin55=PG8
Mcu.Pin56=PC7
Mcu.Pin57=PA10
Mcu.Pin58=PA11
Mcu.Pin59=PA12
Mcu.Pin6=PF1
Mcu.Pin60=PA13
Mcu.Pin61=PH13
Mcu.Pin62=PH15
Mcu.Pin63=PI0
Mcu.Pin64=PI2
Mcu.Pin65=PA14
Mcu.Pin66=PD0
Mcu.Pin67=PD1
Mcu.Pin68=PD7
Mcu.Pin69=PG10
Mcu.Pin7=PF2
Mcu.Pin70=PG11
Mcu.Pin71=PG15
Mcu.Pin72=PB8
Mcu.Pin73=PB9
Mcu.Pin74=PE0
Mcu.Pin75=PE1
Mcu.Pin76=VP_DMA2D_VS_DMA2D
Mcu.Pin77=VP_FATFS_VS_Generic
Mcu.Pin78=VP_RTC_VS_RTC_Activate
Mcu.Pin79=VP_RTC_VS_RTC_Calendar
Mcu.Pin8=PF3
Mcu.Pin80=VP_SYS_VS_Systick
Mcu.Pin9=PF4
Mcu.PinsNb=81
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F767IGTx
MxCube.Version=6.14.1
MxDb.Version=DB.6.0.141
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.EXTI15_10_IRQn=true\:1\:0\:true\:false\:true\:true\:true\:true
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.LTDC_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA10.GPIOParameters=GPIO_Speed,GPIO_PuPd
PA10.GPIO_PuPd=GPIO_PULLUP
PA10.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PA10.Mode=RGB565
PA10.Signal=LTDC_B4
PA11.GPIOParameters=GPIO_Speed,GPIO_PuPd
PA11.GPIO_PuPd=GPIO_PULLUP
PA11.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PA11.Locked=true
PA11.Mode=RGB565
PA11.Signal=LTDC_R4
PA12.GPIOParameters=GPIO_Speed,GPIO_PuPd
PA12.GPIO_PuPd=GPIO_PULLUP
PA12.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PA12.Locked=true
PA12.Mode=RGB565
PA12.Signal=LTDC_R5
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA3.GPIOParameters=GPIO_Speed,GPIO_PuPd
PA3.GPIO_PuPd=GPIO_PULLUP
PA3.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PA3.Mode=RGB565
PA3.Signal=LTDC_B5
PB0.GPIOParameters=GPIO_Speed,GPIO_PuPd
PB0.GPIO_PuPd=GPIO_PULLUP
PB0.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB0.Mode=RGB565
PB0.Signal=LTDC_R3
PB1.GPIOParameters=GPIO_Speed,GPIO_PuPd
PB1.GPIO_PuPd=GPIO_PULLUP
PB1.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB1.Mode=RGB565
PB1.Signal=LTDC_R6
PB10.Mode=Single Bank 1
PB10.Signal=QUADSPI_BK1_NCS
PB2.Locked=true
PB2.Mode=Single Bank 1
PB2.Signal=QUADSPI_CLK
PB8.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_FM8
PB8.GPIO_FM8=SYSCFG_PMC_I2C_PB8_FMP
PB8.GPIO_PuPd=GPIO_PULLUP
PB8.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB8.Mode=RGB565
PB8.Signal=LTDC_B6
PB9.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_FM9
PB9.GPIO_FM9=SYSCFG_PMC_I2C_PB9_FMP
PB9.GPIO_PuPd=GPIO_PULLUP
PB9.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB9.Mode=RGB565
PB9.Signal=LTDC_B7
PC0.Signal=FMC_SDNWE
PC14/OSC32_IN.Mode=LSE-External-Oscillator
PC14/OSC32_IN.Signal=RCC_OSC32_IN
PC15/OSC32_OUT.Mode=LSE-External-Oscillator
PC15/OSC32_OUT.Signal=RCC_OSC32_OUT
PC7.GPIOParameters=GPIO_Speed,GPIO_PuPd
PC7.GPIO_PuPd=GPIO_PULLUP
PC7.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PC7.Locked=true
PC7.Mode=RGB565
PC7.Signal=LTDC_G6
PD0.Signal=FMC_D2_DA2
PD1.Signal=FMC_D3_DA3
PD10.Signal=FMC_D15_DA15
PD13.GPIOParameters=GPIO_PuPd,GPIO_ModeDefaultEXTI
PD13.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_FALLING
PD13.GPIO_PuPd=GPIO_PULLUP
PD13.Locked=true
PD13.Signal=GPXTI13
PD14.Signal=FMC_D0_DA0
PD15.Signal=FMC_D1_DA1
PD7.Locked=true
PD7.Signal=GPIO_Output
PD8.Signal=FMC_D13_DA13
PD9.Signal=FMC_D14_DA14
PE0.Signal=FMC_NBL0
PE1.Signal=FMC_NBL1
PE10.Signal=FMC_D7_DA7
PE11.Signal=FMC_D8_DA8
PE12.Signal=FMC_D9_DA9
PE13.Signal=FMC_D10_DA10
PE14.Signal=FMC_D11_DA11
PE15.Signal=FMC_D12_DA12
PE7.Signal=FMC_D4_DA4
PE8.Signal=FMC_D5_DA5
PE9.Signal=FMC_D6_DA6
PF0.Signal=FMC_A0
PF1.Signal=FMC_A1
PF10.GPIOParameters=GPIO_Speed,GPIO_PuPd
PF10.GPIO_PuPd=GPIO_PULLUP
PF10.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PF10.Mode=RGB565
PF10.Signal=LTDC_DE
PF11.Signal=FMC_SDNRAS
PF12.Signal=FMC_A6
PF13.Signal=FMC_A7
PF14.Signal=FMC_A8
PF15.Signal=FMC_A9
PF2.Signal=FMC_A2
PF3.Signal=FMC_A3
PF4.Signal=FMC_A4
PF5.Signal=FMC_A5
PF6.Mode=Single Bank 1
PF6.Signal=QUADSPI_BK1_IO3
PF7.Locked=true
PF7.Mode=Single Bank 1
PF7.Signal=QUADSPI_BK1_IO2
PF8.Mode=Single Bank 1
PF8.Signal=QUADSPI_BK1_IO0
PF9.Mode=Single Bank 1
PF9.Signal=QUADSPI_BK1_IO1
PG0.Signal=FMC_A10
PG1.Signal=FMC_A11
PG10.GPIOParameters=GPIO_Speed,GPIO_PuPd
PG10.GPIO_PuPd=GPIO_PULLUP
PG10.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PG10.Locked=true
PG10.Mode=RGB565
PG10.Signal=LTDC_G3
PG11.GPIOParameters=GPIO_Speed,GPIO_PuPd
PG11.GPIO_PuPd=GPIO_PULLUP
PG11.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PG11.Locked=true
PG11.Mode=RGB565
PG11.Signal=LTDC_B3
PG15.Signal=FMC_SDNCAS
PG2.Signal=FMC_A12
PG4.Signal=FMC_A14_BA0
PG5.Signal=FMC_A15_BA1
PG6.GPIOParameters=GPIO_Speed,GPIO_PuPd
PG6.GPIO_PuPd=GPIO_PULLUP
PG6.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PG6.Mode=RGB565
PG6.Signal=LTDC_R7
PG7.GPIOParameters=GPIO_Speed,GPIO_PuPd
PG7.GPIO_PuPd=GPIO_PULLUP
PG7.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PG7.Mode=RGB565
PG7.Signal=LTDC_CLK
PG8.Signal=FMC_SDCLK
PH0/OSC_IN.Mode=HSE-External-Oscillator
PH0/OSC_IN.Signal=RCC_OSC_IN
PH1/OSC_OUT.Mode=HSE-External-Oscillator
PH1/OSC_OUT.Signal=RCC_OSC_OUT
PH13.GPIOParameters=GPIO_Speed,GPIO_PuPd
PH13.GPIO_PuPd=GPIO_PULLUP
PH13.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PH13.Locked=true
PH13.Mode=RGB565
PH13.Signal=LTDC_G2
PH15.GPIOParameters=GPIO_Speed,GPIO_PuPd
PH15.GPIO_PuPd=GPIO_PULLUP
PH15.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PH15.Locked=true
PH15.Mode=RGB565
PH15.Signal=LTDC_G4
PH4.Locked=true
PH4.Mode=I2C
PH4.Signal=I2C2_SCL
PH5.Locked=true
PH5.Mode=I2C
PH5.Signal=I2C2_SDA
PH6.Mode=SdramChipSelect2_2
PH6.Signal=FMC_SDNE1
PH7.Mode=SdramChipSelect2_2
PH7.Signal=FMC_SDCKE1
PI0.GPIOParameters=GPIO_Speed,GPIO_PuPd
PI0.GPIO_PuPd=GPIO_PULLUP
PI0.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PI0.Locked=true
PI0.Mode=RGB565
PI0.Signal=LTDC_G5
PI10.GPIOParameters=GPIO_Speed,GPIO_PuPd
PI10.GPIO_PuPd=GPIO_PULLUP
PI10.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PI10.Mode=RGB565
PI10.Signal=LTDC_HSYNC
PI2.GPIOParameters=GPIO_Speed,GPIO_PuPd
PI2.GPIO_PuPd=GPIO_PULLUP
PI2.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PI2.Mode=RGB565
PI2.Signal=LTDC_G7
PI8.GPIOParameters=GPIO_PuPd
PI8.GPIO_PuPd=GPIO_PULLDOWN
PI8.Locked=true
PI8.Signal=GPIO_Output
PI9.GPIOParameters=GPIO_Speed,GPIO_PuPd
PI9.GPIO_PuPd=GPIO_PULLUP
PI9.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PI9.Mode=RGB565
PI9.Signal=LTDC_VSYNC
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F767IGTx
ProjectManager.FirmwarePackage=STM32Cube FW_F7 V1.17.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x400
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=Fatfs.ioc
ProjectManager.ProjectName=Fatfs
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x800
ProjectManager.TargetToolchain=CMake
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA2D_Init-DMA2D-false-HAL-true,4-MX_FMC_Init-FMC-false-HAL-true,5-MX_QUADSPI_Init-QUADSPI-false-HAL-true,6-MX_LTDC_Init-LTDC-false-HAL-true,7-MX_RTC_Init-RTC-false-HAL-true,8-MX_I2C2_Init-I2C2-false-HAL-true,9-MX_FATFS_Init-FATFS-false-HAL-false,0-MX_CORTEX_M7_Init-CORTEX_M7-false-HAL-true
QUADSPI.ChipSelectHighTime=QSPI_CS_HIGH_TIME_6_CYCLE
QUADSPI.ClockPrescaler=1
QUADSPI.FifoThreshold=4
QUADSPI.FlashSize=24
QUADSPI.IPParameters=ClockPrescaler,FifoThreshold,SampleShifting,FlashSize,ChipSelectHighTime
QUADSPI.SampleShifting=QSPI_SAMPLE_SHIFTING_HALFCYCLE
RCC.AHBFreq_Value=*********
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=54000000
RCC.APB1TimFreq_Value=*********
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=*********
RCC.APB2TimFreq_Value=*********
RCC.CECFreq_Value=32786.88524590164
RCC.CortexFreq_Value=*********
RCC.DFSDMAudioFreq_Value=96000000
RCC.DFSDMFreq_Value=*********
RCC.EthernetFreq_Value=*********
RCC.FCLKCortexFreq_Value=*********
RCC.FamilyName=M
RCC.HCLKFreq_Value=*********
RCC.HSE_VALUE=25000000
RCC.HSI_VALUE=16000000
RCC.I2C1Freq_Value=54000000
RCC.I2C2Freq_Value=54000000
RCC.I2C3Freq_Value=54000000
RCC.I2C4Freq_Value=54000000
RCC.I2SFreq_Value=96000000
RCC.IPParameters=AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CECFreq_Value,CortexFreq_Value,DFSDMAudioFreq_Value,DFSDMFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2C1Freq_Value,I2C2Freq_Value,I2C3Freq_Value,I2C4Freq_Value,I2SFreq_Value,LCDTFTFreq_Value,LPTIM1Freq_Value,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLI2SPCLKFreq_Value,PLLI2SQCLKFreq_Value,PLLI2SRCLKFreq_Value,PLLI2SRoutputFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,PLLQoutputFreq_Value,PLLRFreq_Value,PLLSAIPCLKFreq_Value,PLLSAIQCLKFreq_Value,PLLSAIR,PLLSAIRCLKFreq_Value,PLLSAIoutputFreq_Value,PLLSourceVirtual,RNGFreq_Value,RTCClockSelection,RTCFreq_Value,SAI1Freq_Value,SAI2Freq_Value,SDMMC2Freq_Value,SDMMCFreq_Value,SPDIFRXFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,UART4Freq_Value,UART5Freq_Value,UART7Freq_Value,UART8Freq_Value,USART1Freq_Value,USART2Freq_Value,USART3Freq_Value,USART6Freq_Value,USBFreq_Value,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VCOSAIOutputFreq_Value
RCC.LCDTFTFreq_Value=32000000
RCC.LPTIM1Freq_Value=54000000
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=*********
RCC.PLLCLKFreq_Value=*********
RCC.PLLI2SPCLKFreq_Value=96000000
RCC.PLLI2SQCLKFreq_Value=96000000
RCC.PLLI2SRCLKFreq_Value=96000000
RCC.PLLI2SRoutputFreq_Value=96000000
RCC.PLLM=25
RCC.PLLN=432
RCC.PLLQCLKFreq_Value=*********
RCC.PLLQoutputFreq_Value=*********
RCC.PLLRFreq_Value=*********
RCC.PLLSAIPCLKFreq_Value=96000000
RCC.PLLSAIQCLKFreq_Value=96000000
RCC.PLLSAIR=3
RCC.PLLSAIRCLKFreq_Value=64000000
RCC.PLLSAIoutputFreq_Value=96000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.RNGFreq_Value=*********
RCC.RTCClockSelection=RCC_RTCCLKSOURCE_LSE
RCC.RTCFreq_Value=32768
RCC.SAI1Freq_Value=96000000
RCC.SAI2Freq_Value=96000000
RCC.SDMMC2Freq_Value=*********
RCC.SDMMCFreq_Value=*********
RCC.SPDIFRXFreq_Value=96000000
RCC.SYSCLKFreq_VALUE=*********
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.UART4Freq_Value=54000000
RCC.UART5Freq_Value=54000000
RCC.UART7Freq_Value=54000000
RCC.UART8Freq_Value=54000000
RCC.USART1Freq_Value=*********
RCC.USART2Freq_Value=54000000
RCC.USART3Freq_Value=54000000
RCC.USART6Freq_Value=*********
RCC.USBFreq_Value=*********
RCC.VCOI2SOutputFreq_Value=*********
RCC.VCOInputFreq_Value=1000000
RCC.VCOOutputFreq_Value=*********
RCC.VCOSAIOutputFreq_Value=*********
RTC.Format=RTC_FORMAT_BIN
RTC.IPParameters=Format
SH.FMC_A0.0=FMC_A0,13b-sda2
SH.FMC_A0.ConfNb=1
SH.FMC_A1.0=FMC_A1,13b-sda2
SH.FMC_A1.ConfNb=1
SH.FMC_A10.0=FMC_A10,13b-sda2
SH.FMC_A10.ConfNb=1
SH.FMC_A11.0=FMC_A11,13b-sda2
SH.FMC_A11.ConfNb=1
SH.FMC_A12.0=FMC_A12,13b-sda2
SH.FMC_A12.ConfNb=1
SH.FMC_A14_BA0.0=FMC_BA0,FourSdramBanks2
SH.FMC_A14_BA0.ConfNb=1
SH.FMC_A15_BA1.0=FMC_BA1,FourSdramBanks2
SH.FMC_A15_BA1.ConfNb=1
SH.FMC_A2.0=FMC_A2,13b-sda2
SH.FMC_A2.ConfNb=1
SH.FMC_A3.0=FMC_A3,13b-sda2
SH.FMC_A3.ConfNb=1
SH.FMC_A4.0=FMC_A4,13b-sda2
SH.FMC_A4.ConfNb=1
SH.FMC_A5.0=FMC_A5,13b-sda2
SH.FMC_A5.ConfNb=1
SH.FMC_A6.0=FMC_A6,13b-sda2
SH.FMC_A6.ConfNb=1
SH.FMC_A7.0=FMC_A7,13b-sda2
SH.FMC_A7.ConfNb=1
SH.FMC_A8.0=FMC_A8,13b-sda2
SH.FMC_A8.ConfNb=1
SH.FMC_A9.0=FMC_A9,13b-sda2
SH.FMC_A9.ConfNb=1
SH.FMC_D0_DA0.0=FMC_D0,sd-16b-d2
SH.FMC_D0_DA0.ConfNb=1
SH.FMC_D10_DA10.0=FMC_D10,sd-16b-d2
SH.FMC_D10_DA10.ConfNb=1
SH.FMC_D11_DA11.0=FMC_D11,sd-16b-d2
SH.FMC_D11_DA11.ConfNb=1
SH.FMC_D12_DA12.0=FMC_D12,sd-16b-d2
SH.FMC_D12_DA12.ConfNb=1
SH.FMC_D13_DA13.0=FMC_D13,sd-16b-d2
SH.FMC_D13_DA13.ConfNb=1
SH.FMC_D14_DA14.0=FMC_D14,sd-16b-d2
SH.FMC_D14_DA14.ConfNb=1
SH.FMC_D15_DA15.0=FMC_D15,sd-16b-d2
SH.FMC_D15_DA15.ConfNb=1
SH.FMC_D1_DA1.0=FMC_D1,sd-16b-d2
SH.FMC_D1_DA1.ConfNb=1
SH.FMC_D2_DA2.0=FMC_D2,sd-16b-d2
SH.FMC_D2_DA2.ConfNb=1
SH.FMC_D3_DA3.0=FMC_D3,sd-16b-d2
SH.FMC_D3_DA3.ConfNb=1
SH.FMC_D4_DA4.0=FMC_D4,sd-16b-d2
SH.FMC_D4_DA4.ConfNb=1
SH.FMC_D5_DA5.0=FMC_D5,sd-16b-d2
SH.FMC_D5_DA5.ConfNb=1
SH.FMC_D6_DA6.0=FMC_D6,sd-16b-d2
SH.FMC_D6_DA6.ConfNb=1
SH.FMC_D7_DA7.0=FMC_D7,sd-16b-d2
SH.FMC_D7_DA7.ConfNb=1
SH.FMC_D8_DA8.0=FMC_D8,sd-16b-d2
SH.FMC_D8_DA8.ConfNb=1
SH.FMC_D9_DA9.0=FMC_D9,sd-16b-d2
SH.FMC_D9_DA9.ConfNb=1
SH.FMC_NBL0.0=FMC_NBL0,Sd2ByteEnable2
SH.FMC_NBL0.ConfNb=1
SH.FMC_NBL1.0=FMC_NBL1,Sd2ByteEnable2
SH.FMC_NBL1.ConfNb=1
SH.FMC_SDCLK.0=FMC_SDCLK,13b-sda2
SH.FMC_SDCLK.ConfNb=1
SH.FMC_SDNCAS.0=FMC_SDNCAS,13b-sda2
SH.FMC_SDNCAS.ConfNb=1
SH.FMC_SDNRAS.0=FMC_SDNRAS,13b-sda2
SH.FMC_SDNRAS.ConfNb=1
SH.FMC_SDNWE.0=FMC_SDNWE,13b-sda2
SH.FMC_SDNWE.ConfNb=1
SH.GPXTI13.0=GPIO_EXTI13
SH.GPXTI13.ConfNb=1
VP_DMA2D_VS_DMA2D.Mode=DMA2D_Activate
VP_DMA2D_VS_DMA2D.Signal=DMA2D_VS_DMA2D
VP_FATFS_VS_Generic.Mode=User_defined
VP_FATFS_VS_Generic.Signal=FATFS_VS_Generic
VP_RTC_VS_RTC_Activate.Mode=RTC_Enabled
VP_RTC_VS_RTC_Activate.Signal=RTC_VS_RTC_Activate
VP_RTC_VS_RTC_Calendar.Mode=RTC_Calendar
VP_RTC_VS_RTC_Calendar.Signal=RTC_VS_RTC_Calendar
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
board=custom
